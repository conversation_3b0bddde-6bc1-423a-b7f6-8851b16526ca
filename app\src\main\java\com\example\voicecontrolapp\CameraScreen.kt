package com.example.voicecontrolapp

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Videocam
import androidx.compose.material.icons.filled.VideocamOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import kotlinx.coroutines.delay

@Composable
fun CameraScreen(
    context: Context = LocalContext.current,
    viewModel: CameraViewModel = viewModel()
) {
    // 初始化ViewModel
    LaunchedEffect(Unit) {
        viewModel.initialize(context)
    }
    
    val isConnected by viewModel.isConnected
    val cameraUrl by viewModel.cameraUrl
    val isStreaming by viewModel.isStreaming
    val errorMessage by viewModel.errorMessage
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        
        // 标题和控制区域
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "机械狗摄像头",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(
                    onClick = { viewModel.refreshCamera() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新摄像头"
                    )
                }
                
                IconButton(
                    onClick = { 
                        if (isStreaming) {
                            viewModel.stopStream()
                        } else {
                            viewModel.startStream()
                        }
                    }
                ) {
                    Icon(
                        imageVector = if (isStreaming) Icons.Default.VideocamOff else Icons.Default.Videocam,
                        contentDescription = if (isStreaming) "停止直播" else "开始直播"
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 状态指示器
        StatusIndicator(
            isConnected = isConnected,
            isStreaming = isStreaming,
            errorMessage = errorMessage
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 摄像头视频区域
        CameraVideoArea(
            cameraUrl = cameraUrl,
            isStreaming = isStreaming,
            isConnected = isConnected,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 摄像头设置
        CameraSettings(
            onUrlChange = { viewModel.updateCameraUrl(it) },
            currentUrl = cameraUrl
        )
    }
}

@Composable
fun StatusIndicator(
    isConnected: Boolean,
    isStreaming: Boolean,
    errorMessage: String?
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                errorMessage != null -> MaterialTheme.colorScheme.errorContainer
                isStreaming -> MaterialTheme.colorScheme.primaryContainer
                isConnected -> MaterialTheme.colorScheme.secondaryContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = when {
                            errorMessage != null -> MaterialTheme.colorScheme.error
                            isStreaming -> Color.Green
                            isConnected -> MaterialTheme.colorScheme.primary
                            else -> Color.Gray
                        },
                        shape = androidx.compose.foundation.shape.CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Text(
                text = when {
                    errorMessage != null -> "错误: $errorMessage"
                    isStreaming -> "直播中"
                    isConnected -> "已连接，点击开始直播"
                    else -> "未连接到机械狗"
                },
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
fun CameraVideoArea(
    cameraUrl: String,
    isStreaming: Boolean,
    isConnected: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            when {
                isStreaming && cameraUrl.isNotEmpty() -> {
                    // 使用MjpegStreamDisplay加载MJPEG流
                    MjpegStreamDisplay(
                        url = cameraUrl,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(8.dp))
                    )
                }
                
                !isConnected -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.VideocamOff,
                            contentDescription = "摄像头离线",
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "机械狗未连接",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "请检查网络连接和设置",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                else -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Videocam,
                            contentDescription = "摄像头准备",
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "摄像头准备就绪",
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "点击开始按钮开始直播",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MjpegStreamDisplay(
    url: String,
    modifier: Modifier = Modifier
) {
    var isLoading by remember(url) { mutableStateOf(true) }
    var hasError by remember(url) { mutableStateOf(false) }
    var useExoPlayer by remember { mutableStateOf(true) } // 默认使用ExoPlayer
    var exoPlayer by remember { mutableStateOf<ExoPlayer?>(null) }
    val context = LocalContext.current
    
    // 当URL改变时重置状态
    LaunchedEffect(url) {
        isLoading = true
        hasError = false
        
        // 释放之前的ExoPlayer
        exoPlayer?.release()
        exoPlayer = null
        
        // 设置超时，如果10秒后还在加载则认为失败
        delay(10000)
        if (isLoading) {
            Log.w("MjpegStream", "加载超时，设置为错误状态")
            isLoading = false
            hasError = true
        }
    }
    
    DisposableEffect(Unit) {
        onDispose {
            exoPlayer?.release()
        }
    }
    
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        if (useExoPlayer) {
            // ExoPlayer方案 - 专业视频流处理，支持MJPEG
            AndroidView(
                factory = { ctx ->
                    PlayerView(ctx).apply {
                        setBackgroundColor(android.graphics.Color.BLACK)
                        useController = false // 隐藏播放控制器
                        
                        // 创建ExoPlayer实例
                        val player = ExoPlayer.Builder(ctx).build()
                        this.player = player
                        exoPlayer = player
                        
                        // 设置播放器监听器
                        player.addListener(object : Player.Listener {
                            override fun onPlaybackStateChanged(playbackState: Int) {
                                when (playbackState) {
                                    Player.STATE_READY -> {
                                        Log.d("MjpegStream", "ExoPlayer准备就绪")
                                        isLoading = false
                                        hasError = false
                                    }
                                    Player.STATE_BUFFERING -> {
                                        Log.d("MjpegStream", "ExoPlayer缓冲中")
                                        isLoading = true
                                        hasError = false
                                    }
                                    Player.STATE_ENDED -> {
                                        Log.d("MjpegStream", "ExoPlayer播放结束")
                                    }
                                    Player.STATE_IDLE -> {
                                        Log.d("MjpegStream", "ExoPlayer空闲状态")
                                    }
                                }
                            }
                            
                            override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                                Log.e("MjpegStream", "ExoPlayer错误: ${error.message}")
                                isLoading = false
                                hasError = true
                            }
                        })
                        
                        // 设置媒体项并开始播放
                        val mediaItem = MediaItem.fromUri(url)
                        player.setMediaItem(mediaItem)
                        player.prepare()
                        player.play()
                        
                        Log.d("MjpegStream", "ExoPlayer初始化完成，开始播放: $url")
                    }
                },
                update = { playerView ->
                    Log.d("MjpegStream", "更新ExoPlayer，URL: $url")
                    exoPlayer?.let { player ->
                        val mediaItem = MediaItem.fromUri(url)
                        player.setMediaItem(mediaItem)
                        player.prepare()
                        player.play()
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
        } else {
            // WebView备选方案
            AndroidView(
                factory = { ctx ->
                    WebView(ctx).apply {
                        webViewClient = object : WebViewClient() {
                            override fun onPageFinished(view: WebView?, url: String?) {
                                Log.d("MjpegStream", "WebView页面加载完成: $url")
                                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                    isLoading = false
                                    hasError = false
                                }, 2000)
                            }
                            
                            override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                                Log.e("MjpegStream", "WebView加载错误: $description")
                                isLoading = false
                                hasError = true
                            }
                        }
                        settings.apply {
                            javaScriptEnabled = false
                            loadWithOverviewMode = true
                            useWideViewPort = true
                        }
                        setBackgroundColor(android.graphics.Color.BLACK)
                    }
                },
                update = { webView ->
                    Log.d("MjpegStream", "使用WebView加载MJPEG流: $url")
                    val html = """
                        <html>
                        <head>
                            <style>
                                body { margin: 0; padding: 0; background: black; display: flex; justify-content: center; align-items: center; height: 100vh; }
                                img { max-width: 100%; max-height: 100%; object-fit: contain; }
                            </style>
                        </head>
                        <body>
                            <img src="$url" alt="MJPEG Stream" onerror="this.style.display='none'" />
                        </body>
                        </html>
                    """.trimIndent()
                    webView.loadData(html, "text/html", "UTF-8")
                },
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 显示加载状态
        if (isLoading) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(
                    color = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = if (useExoPlayer) "ExoPlayer连接中..." else "WebView连接中...",
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
        
        // 显示错误状态
        if (hasError && !isLoading) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.VideocamOff,
                    contentDescription = "连接失败",
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "无法连接到摄像头",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyMedium
                )
                
                OutlinedButton(
                    onClick = {
                        useExoPlayer = !useExoPlayer
                        isLoading = true
                        hasError = false
                        exoPlayer?.release()
                        exoPlayer = null
                    },
                    modifier = Modifier.padding(top = 8.dp)
                ) {
                    Text(if (useExoPlayer) "切换到WebView" else "切换到ExoPlayer")
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "URL: $url",
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    style = MaterialTheme.typography.bodySmall,
                    maxLines = 2
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedButton(
                    onClick = {
                        isLoading = true
                        hasError = false
                        exoPlayer?.release()
                        exoPlayer = null
                        Log.d("MjpegStream", "手动重新连接")
                    }
                ) {
                    Text("重新连接")
                }
            }
        }
    }
}

@Composable
fun CameraSettings(
    onUrlChange: (String) -> Unit,
    currentUrl: String
) {
    var showSettings by remember { mutableStateOf(false) }
    var urlInput by remember { mutableStateOf(currentUrl) }
    
    Column {
        OutlinedButton(
            onClick = { showSettings = !showSettings },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(if (showSettings) "隐藏设置" else "摄像头设置")
        }
        
        if (showSettings) {
            Spacer(modifier = Modifier.height(8.dp))
            
            OutlinedTextField(
                value = urlInput,
                onValueChange = { urlInput = it },
                label = { Text("摄像头流地址") },
                placeholder = { Text("例如: http://192.168.110.21:8080/remote_camera_feed") },
                supportingText = {
                    Text("输入机械狗远程摄像头的MJPEG流地址")
                },
                modifier = Modifier.fillMaxWidth(),
                trailingIcon = {
                    if (urlInput != currentUrl && urlInput.isNotBlank()) {
                        IconButton(
                            onClick = {
                                onUrlChange(urlInput)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = "应用设置"
                            )
                        }
                    }
                }
            )
        }
    }
}