/ Header Record For PersistentHashMapValueStorage androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity kotlin.Enum androidx.lifecycle.ViewModel* )com.example.voicecontrolapp.NetworkResult* )com.example.voicecontrolapp.NetworkResult kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum androidx.lifecycle.ViewModel* )com.example.voicecontrolapp.NetworkResult* )com.example.voicecontrolapp.NetworkResult androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel* )com.example.voicecontrolapp.NetworkResult* )com.example.voicecontrolapp.NetworkResult