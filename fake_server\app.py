import os
import urllib.parse
import requests
from flask import Flask, request, jsonify, render_template, send_from_directory, Response, stream_with_context
from flask_cors import CORS
import time
import json
import logging
from datetime import datetime
from pydub import AudioSegment
import tempfile

# 配置应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fake_server')

# 配置目录
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
VOICE_FOLDER = os.path.join(UPLOAD_FOLDER, 'voice')
VIDEO_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'videos')
COMMAND_LOG = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'commands.json')

# 远程摄像头URL
REMOTE_CAMERA_URL = "http://192.168.110.114:5001/video_feed"

# 确保必要的目录存在
os.makedirs(VOICE_FOLDER, exist_ok=True)
os.makedirs(VIDEO_FOLDER, exist_ok=True)
os.makedirs(os.path.dirname(COMMAND_LOG), exist_ok=True)

# 如果命令日志不存在，创建一个空的日志文件
if not os.path.exists(COMMAND_LOG):
    with open(COMMAND_LOG, 'w') as f:
        json.dump([], f)

@app.route('/')
def index():
    """提供简单的Web界面"""
    return render_template('index.html')

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查API，用于确认服务器是否在线"""
    return jsonify({
        "status": "ok",
        "timestamp": time.time()
    }), 200

@app.route('/api/upload_voice', methods=['POST'])
def upload_voice():
    """接收并保存语音文件"""
    logger.info("收到语音上传请求")
    
    try:
        if 'voice' not in request.files:
            logger.warning("没有收到语音文件")
            return jsonify({"error": "No file provided"}), 400
            
        voice_file = request.files['voice']
        
        if voice_file.filename == '':
            logger.warning("文件名为空")
            return jsonify({"error": "No file selected"}), 400
        
        record_path = os.path.join(VOICE_FOLDER, 'record.mp3')
        if os.path.exists(record_path):
            os.remove(record_path)
            logger.info("已删除旧的 record.mp3")
        
        # 保存文件
        voice_file.save(record_path)
        logger.info(f"语音文件已保存为: {record_path}")
        
        # 调用处理程序
        from dog_agent_handler import process_voice
        result = process_voice()
        
        if result:
            return jsonify({
                "status": "success", 
                "message": "Voice command processed successfully"
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "Failed to process voice command"
            }), 500
            
    except Exception as e:
        logger.error(f"处理上传文件失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/control', methods=['POST'])
def control_dog():
    """接收控制命令"""
    data = request.json
    
    if not data or 'action' not in data:
        return jsonify({"error": "No action specified"}), 400
    
    action = data['action']
    timestamp = data.get('timestamp', time.time())
    source = data.get('source', 'unknown')
    
    logger.info(f"收到控制命令: {action}, 来源: {source}")
    
    # 记录命令到日志文件
    try:
        with open(COMMAND_LOG, 'r') as f:
            commands = json.load(f)
        
        commands.append({
            "action": action,
            "timestamp": timestamp,
            "source": source,
            "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
        # 只保留最近的50条命令
        if len(commands) > 50:
            commands = commands[-50:]
        
        with open(COMMAND_LOG, 'w') as f:
            json.dump(commands, f, indent=2)
    
    except Exception as e:
        logger.error(f"记录命令失败: {str(e)}")
    
    return jsonify({
        "status": "success",
        "message": f"Command '{action}' received",
        "command_executed": action
    }), 200

@app.route('/api/video_stream')
def video_stream():
    """提供视频流页面"""
    # 获取可用的视频文件列表
    videos = []
    if os.path.exists(VIDEO_FOLDER):
        try:
            for f in os.listdir(VIDEO_FOLDER):
                if f.lower().endswith(('.mp4', '.webm', '.avi', '.mov')):
                    videos.append(f)
            logger.info(f"找到视频文件: {videos}")
        except Exception as e:
            logger.error(f"读取视频目录失败: {str(e)}")
    
    # 添加一个特殊选项用于远程摄像头
    has_remote_camera = True
    
    return render_template('video_stream.html', videos=videos, has_remote_camera=has_remote_camera)

@app.route('/videos/<path:filename>')
def serve_video(filename):
    """提供视频文件服务"""
    try:
        # URL解码文件名以处理中文
        decoded_filename = urllib.parse.unquote(filename)
        logger.info(f"请求视频文件: {decoded_filename}")
        
        # 检查文件是否存在
        file_path = os.path.join(VIDEO_FOLDER, decoded_filename)
        if not os.path.exists(file_path):
            logger.error(f"视频文件不存在: {file_path}")
            return jsonify({"error": "Video file not found"}), 404
        
        # 根据文件扩展名设置正确的MIME类型
        ext = decoded_filename.lower().split('.')[-1]
        mimetype_map = {
            'mp4': 'video/mp4',
            'webm': 'video/webm',
            'avi': 'video/x-msvideo',
            'mov': 'video/quicktime'
        }
        mimetype = mimetype_map.get(ext, 'video/mp4')
        
        logger.info(f"提供视频文件: {file_path}, MIME类型: {mimetype}")
        
        # 使用Response来更好地控制视频流
        def generate():
            with open(file_path, 'rb') as f:
                data = f.read(1024)
                while data:
                    yield data
                    data = f.read(1024)
        
        response = Response(generate(), mimetype=mimetype)
        response.headers.add('Accept-Ranges', 'bytes')
        response.headers.add('Content-Length', str(os.path.getsize(file_path)))
        
        return response
        
    except Exception as e:
        logger.error(f"提供视频文件时出错: {str(e)}")
        return jsonify({"error": "Error serving video file"}), 500

@app.route('/api/commands', methods=['GET'])
def get_commands():
    """获取最近的命令历史"""
    try:
        with open(COMMAND_LOG, 'r') as f:
            commands = json.load(f)
        return jsonify(commands), 200
    except Exception as e:
        logger.error(f"获取命令历史失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/voice_files', methods=['GET'])
def get_voice_files():
    """获取语音文件列表"""
    try:
        voice_files = []
        if os.path.exists(VOICE_FOLDER):
            for f in os.listdir(VOICE_FOLDER):
                if f.lower().endswith(('.mp3', '.wav', '.m4a')):
                    file_path = os.path.join(VOICE_FOLDER, f)
                    file_stats = os.stat(file_path)
                    
                    # 获取音频文件时长（需要安装 mutagen）
                    duration = "未知"
                    try:
                        from mutagen.mp3 import MP3
                        audio = MP3(file_path)
                        duration = f"{int(audio.info.length)}秒"
                    except:
                        pass
                    
                    voice_files.append({
                        "filename": f,
                        "size": file_stats.st_size,
                        "duration": duration,
                        "upload_time": datetime.fromtimestamp(file_stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                        "url": f"/voice/{f}",
                        "full_path": file_path
                    })
        
        # 按时间排序，最新的在前面
        voice_files.sort(key=lambda x: x['upload_time'], reverse=True)
        
        return jsonify({
            "status": "success",
            "voice_files": voice_files,
            "count": len(voice_files)
        }), 200
        
    except Exception as e:
        logger.error(f"获取语音文件列表失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "voice_files": [],
            "count": 0
        }), 500

@app.route('/voice/<filename>')
def serve_voice(filename):
    """提供语音文件服务"""
    try:
        # 获取文件扩展名
        ext = filename.lower().split('.')[-1]
        
        # 设置对应的 MIME 类型
        mime_types = {
            'mp3': 'audio/mpeg',
            'wav': 'audio/wav',
            'm4a': 'audio/mp4',
            '3gp': 'audio/3gpp'
        }
        mimetype = mime_types.get(ext, 'audio/mpeg')
        
        # 确保文件存在
        file_path = os.path.join(VOICE_FOLDER, filename)
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return jsonify({"error": "File not found"}), 404
            
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.error(f"文件大小为0: {file_path}")
            return jsonify({"error": "Empty file"}), 400
            
        logger.info(f"提供音频文件: {filename}, 大小: {file_size} bytes, MIME类型: {mimetype}")
        
        # 添加必要的响应头
        response = send_from_directory(
            VOICE_FOLDER, 
            filename, 
            mimetype=mimetype,
            as_attachment=False
        )
        
        response.headers['Accept-Ranges'] = 'bytes'
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['Content-Length'] = file_size
        
        return response
        
    except Exception as e:
        logger.error(f"提供语音文件时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/video_list', methods=['GET'])
def get_video_list():
    """获取可用的视频文件列表"""
    try:
        videos = []
        if os.path.exists(VIDEO_FOLDER):
            for f in os.listdir(VIDEO_FOLDER):
                if f.lower().endswith(('.mp4', '.webm', '.avi', '.mov')):
                    file_path = os.path.join(VIDEO_FOLDER, f)
                    file_size = os.path.getsize(file_path)
                    videos.append({
                        "filename": f,
                        "url": f"/videos/{urllib.parse.quote(f)}",
                        "size": file_size
                    })
        
        logger.info(f"返回视频列表: {len(videos)} 个文件")
        return jsonify({
            "status": "success",
            "videos": videos,
            "count": len(videos)
        }), 200
        
    except Exception as e:
        logger.error(f"获取视频列表失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/video_feed')
def video_feed():
    """提供默认视频流（用于app访问）"""
    try:
        # 获取第一个可用的视频文件
        if os.path.exists(VIDEO_FOLDER):
            videos = [f for f in os.listdir(VIDEO_FOLDER) if f.lower().endswith(('.mp4', '.webm', '.avi', '.mov'))]
            if videos:
                # 返回第一个视频的URL
                first_video = videos[0]
                video_url = f"/videos/{urllib.parse.quote(first_video)}"
                return jsonify({
                    "status": "success",
                    "video_url": video_url,
                    "filename": first_video
                }), 200
        
        return jsonify({
            "status": "error",
            "message": "No video files available"
        }), 404
        
    except Exception as e:
        logger.error(f"获取视频流失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/video_feed')
def mjpeg_video_feed():
    """提供MJPEG视频流（模拟摄像头直播）"""
    try:
        # 获取第一个可用的视频文件
        if os.path.exists(VIDEO_FOLDER):
            videos = [f for f in os.listdir(VIDEO_FOLDER) if f.lower().endswith(('.mp4', '.webm', '.avi', '.mov'))]
            if videos:
                first_video = videos[0]
                logger.info(f"开始MJPEG视频流: {first_video}")
                return Response(
                    generate_mjpeg_stream(first_video),
                    mimetype='multipart/x-mixed-replace; boundary=frame'
                )
        
        # 如果没有视频文件，返回测试图像
        return Response(
            generate_test_mjpeg_stream(),
            mimetype='multipart/x-mixed-replace; boundary=frame'
        )
        
    except Exception as e:
        logger.error(f"MJPEG视频流失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

def generate_mjpeg_stream(video_filename):
    """生成MJPEG视频流"""
    try:
        import cv2
        import time
        
        video_path = os.path.join(VIDEO_FOLDER, video_filename)
        logger.info(f"开始处理视频文件: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return
        
        # 获取视频帧率
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 25  # 默认帧率
        
        frame_delay = 1.0 / fps
        logger.info(f"视频帧率: {fps} FPS, 帧间隔: {frame_delay}s")
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                # 视频结束，重新开始
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            
            # 调整帧大小以提高传输效率
            frame = cv2.resize(frame, (640, 480))
            
            # 编码为JPEG
            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            if not ret:
                continue
                
            frame_bytes = buffer.tobytes()
            
            # 生成MJPEG帧
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            
            frame_count += 1
            if frame_count % 100 == 0:
                logger.info(f"已处理 {frame_count} 帧")
            
            time.sleep(frame_delay)
            
    except ImportError:
        logger.warning("OpenCV未安装，使用简化的测试流")
        yield from generate_simple_mjpeg_stream(video_filename)
    except Exception as e:
        logger.error(f"视频流处理失败: {str(e)}")
        yield from generate_simple_mjpeg_stream(video_filename)
    finally:
        if 'cap' in locals():
            cap.release()

@app.route('/remote_camera_feed')
def remote_camera_feed():
    """代理远程MJPEG摄像头流"""
    try:
        logger.info(f"尝试连接远程摄像头: {REMOTE_CAMERA_URL}")
        
        # 使用简单的请求头
        headers = {
            'Accept': 'multipart/x-mixed-replace;boundary=frame'
        }
        
        # 获取远程MJPEG流
        resp = requests.get(REMOTE_CAMERA_URL, 
                          stream=True, 
                          timeout=10,
                          headers=headers)
        
        if resp.status_code != 200:
            logger.error(f"远程摄像头返回错误状态码: {resp.status_code}")
            return jsonify({"error": f"Remote camera returned status code {resp.status_code}"}), 500
            
        logger.info("成功连接到远程摄像头流")
        
        # 直接转发视频流
        return Response(
            stream_with_context(resp.iter_content(chunk_size=1024)),
            mimetype='multipart/x-mixed-replace; boundary=frame'
        )
        
    except requests.RequestException as e:
        logger.error(f"连接远程摄像头失败: {str(e)}")
        return jsonify({"error": f"Failed to connect to remote camera: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"处理远程摄像头流失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    port = 8080
    logger.info(f"启动仿真服务器在端口 {port}")
    logger.info(f"语音文件将保存到: {VOICE_FOLDER}")
    logger.info(f"可以访问 http://localhost:{port} 查看Web界面")
    app.run(host='0.0.0.0', port=port, debug=True) 