  Manifest android  RECORD_AUDIO android.Manifest.permission  Activity android.app  VoiceControlApp android.app.Activity  VoiceControlAppTheme android.app.Activity  enableEdgeToEdge android.app.Activity  getValue android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  
viewModels android.app.Activity  Context android.content  SharedPreferences android.content  MODE_PRIVATE android.content.Context  VoiceControlApp android.content.Context  VoiceControlAppTheme android.content.Context  applicationContext android.content.Context  enableEdgeToEdge android.content.Context  getExternalFilesDir android.content.Context  getSharedPreferences android.content.Context  getValue android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  
viewModels android.content.Context  VoiceControlApp android.content.ContextWrapper  VoiceControlAppTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  getValue android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  
viewModels android.content.ContextWrapper  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  
MediaRecorder 
android.media  Log android.media.MediaRecorder  
MediaRecorder android.media.MediaRecorder  TAG android.media.MediaRecorder  apply android.media.MediaRecorder  isRecording android.media.MediaRecorder  
outputFile android.media.MediaRecorder  prepare android.media.MediaRecorder  release android.media.MediaRecorder  setAudioEncoder android.media.MediaRecorder  setAudioSource android.media.MediaRecorder  
setOutputFile android.media.MediaRecorder  setOutputFormat android.media.MediaRecorder  start android.media.MediaRecorder  stop android.media.MediaRecorder  AMR_NB (android.media.MediaRecorder.AudioEncoder  MIC 'android.media.MediaRecorder.AudioSource  	THREE_GPP (android.media.MediaRecorder.OutputFormat  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  VoiceControlApp  android.view.ContextThemeWrapper  VoiceControlAppTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
viewModels androidx.activity  VoiceControlApp #androidx.activity.ComponentActivity  VoiceControlAppTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  CameraScreen /androidx.compose.animation.AnimatedContentScope  
ControlScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  VoiceScreen /androidx.compose.animation.AnimatedContentScope  animateFloatAsState androidx.compose.animation.core  BorderStroke androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  PressGestureScope $androidx.compose.foundation.gestures  awaitEachGesture $androidx.compose.foundation.gestures  awaitFirstDown $androidx.compose.foundation.gestures  detectDragGestures $androidx.compose.foundation.gestures  detectTapGestures $androidx.compose.foundation.gestures  waitForUpOrCancellation $androidx.compose.foundation.gestures  awaitRelease 6androidx.compose.foundation.gestures.PressGestureScope  ActionButton "androidx.compose.foundation.layout  ActionControlArea "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  ApiEndpointItem "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  CameraScreen "androidx.compose.foundation.layout  CameraSettings "androidx.compose.foundation.layout  CameraVideoArea "androidx.compose.foundation.layout  CameraViewModel "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  CommandHistoryCard "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ConnectionStatusCard "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  
ControlButton "androidx.compose.foundation.layout  
ControlScreen "androidx.compose.foundation.layout  ControlViewModel "androidx.compose.foundation.layout  DirectionControlArea "androidx.compose.foundation.layout  
ExpandLess "androidx.compose.foundation.layout  
ExpandMore "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExperimentalPermissionsApi "androidx.compose.foundation.layout  
FontFamily "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Home "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardActions "androidx.compose.foundation.layout  KeyboardArrowDown "androidx.compose.foundation.layout  KeyboardArrowLeft "androidx.compose.foundation.layout  KeyboardArrowRight "androidx.compose.foundation.layout  KeyboardArrowUp "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  MainContent "androidx.compose.foundation.layout  
MainViewModel "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Mic "androidx.compose.foundation.layout  MicOff "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
NavController "androidx.compose.foundation.layout  
NavigationBar "androidx.compose.foundation.layout  NavigationBarItem "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PermissionRequestCard "androidx.compose.foundation.layout  Pets "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  Refresh "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Screen "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsScreen "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  	StatusBar "androidx.compose.foundation.layout  StatusIndicator "androidx.compose.foundation.layout  
StatusMessage "androidx.compose.foundation.layout  Stop "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TouchApp "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  UsageInstructions "androidx.compose.foundation.layout  Videocam "androidx.compose.foundation.layout  VoiceButton "androidx.compose.foundation.layout  VoiceControlApp "androidx.compose.foundation.layout  VoiceControlAppTheme "androidx.compose.foundation.layout  VoiceScreen "androidx.compose.foundation.layout  Wifi "androidx.compose.foundation.layout  WifiOff "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  takeLast "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Mic +androidx.compose.foundation.layout.BoxScope  MicOff +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  Videocam +androidx.compose.foundation.layout.BoxScope  VideocamOff +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  ActionButton .androidx.compose.foundation.layout.ColumnScope  ActionControlArea .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  ApiEndpointItem .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  CameraSettings .androidx.compose.foundation.layout.ColumnScope  CameraVideoArea .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  CommandHistoryCard .androidx.compose.foundation.layout.ColumnScope  ConnectionStatusCard .androidx.compose.foundation.layout.ColumnScope  
ControlButton .androidx.compose.foundation.layout.ColumnScope  DirectionControlArea .androidx.compose.foundation.layout.ColumnScope  
ExpandLess .androidx.compose.foundation.layout.ColumnScope  
ExpandMore .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Home .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  	ImeAction .androidx.compose.foundation.layout.ColumnScope  KeyboardActions .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowLeft .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowRight .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowUp .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  MainContent .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Mic .androidx.compose.foundation.layout.ColumnScope  MicOff .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PermissionRequestCard .androidx.compose.foundation.layout.ColumnScope  Pets .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Save .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  	StatusBar .androidx.compose.foundation.layout.ColumnScope  StatusIndicator .androidx.compose.foundation.layout.ColumnScope  
StatusMessage .androidx.compose.foundation.layout.ColumnScope  Stop .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  UsageInstructions .androidx.compose.foundation.layout.ColumnScope  Videocam .androidx.compose.foundation.layout.ColumnScope  VideocamOff .androidx.compose.foundation.layout.ColumnScope  VoiceButton .androidx.compose.foundation.layout.ColumnScope  Wifi .androidx.compose.foundation.layout.ColumnScope  WifiOff .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  delay .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  	isGranted .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  shouldShowRationale .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  takeLast .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  ActionButton +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  
ControlButton +androidx.compose.foundation.layout.RowScope  
ExpandLess +androidx.compose.foundation.layout.RowScope  
ExpandMore +androidx.compose.foundation.layout.RowScope  
FontFamily +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Home +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  KeyboardArrowLeft +androidx.compose.foundation.layout.RowScope  KeyboardArrowRight +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Pets +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Save +androidx.compose.foundation.layout.RowScope  Screen +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Stop +androidx.compose.foundation.layout.RowScope  Surface +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  Videocam +androidx.compose.foundation.layout.RowScope  VideocamOff +androidx.compose.foundation.layout.RowScope  Wifi +androidx.compose.foundation.layout.RowScope  WifiOff +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  delay +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  forEach +androidx.compose.foundation.layout.RowScope  launch +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Check ,androidx.compose.material.icons.Icons.Filled  
ExpandLess ,androidx.compose.material.icons.Icons.Filled  
ExpandMore ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowLeft ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowRight ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowUp ,androidx.compose.material.icons.Icons.Filled  Mic ,androidx.compose.material.icons.Icons.Filled  MicOff ,androidx.compose.material.icons.Icons.Filled  Pets ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Save ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Stop ,androidx.compose.material.icons.Icons.Filled  TouchApp ,androidx.compose.material.icons.Icons.Filled  Videocam ,androidx.compose.material.icons.Icons.Filled  VideocamOff ,androidx.compose.material.icons.Icons.Filled  Wifi ,androidx.compose.material.icons.Icons.Filled  WifiOff ,androidx.compose.material.icons.Icons.Filled  ActionButton &androidx.compose.material.icons.filled  ActionControlArea &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Bundle &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  CameraScreen &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  CommandHistoryCard &androidx.compose.material.icons.filled  ComponentActivity &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ConnectionStatusCard &androidx.compose.material.icons.filled  Context &androidx.compose.material.icons.filled  
ControlButton &androidx.compose.material.icons.filled  
ControlScreen &androidx.compose.material.icons.filled  ControlViewModel &androidx.compose.material.icons.filled  DirectionControlArea &androidx.compose.material.icons.filled  
ExpandLess &androidx.compose.material.icons.filled  
ExpandMore &androidx.compose.material.icons.filled  ExperimentalPermissionsApi &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Int &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  KeyboardArrowLeft &androidx.compose.material.icons.filled  KeyboardArrowRight &androidx.compose.material.icons.filled  KeyboardArrowUp &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  Log &androidx.compose.material.icons.filled  MainContent &androidx.compose.material.icons.filled  
MainViewModel &androidx.compose.material.icons.filled  Manifest &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Mic &androidx.compose.material.icons.filled  MicOff &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  
NavController &androidx.compose.material.icons.filled  
NavigationBar &androidx.compose.material.icons.filled  NavigationBarItem &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  PermissionRequestCard &androidx.compose.material.icons.filled  Pets &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  Preview &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Save &androidx.compose.material.icons.filled  Scaffold &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsScreen &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  	StatusBar &androidx.compose.material.icons.filled  
StatusMessage &androidx.compose.material.icons.filled  Stop &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  TouchApp &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  UsageInstructions &androidx.compose.material.icons.filled  Videocam &androidx.compose.material.icons.filled  VideocamOff &androidx.compose.material.icons.filled  VoiceButton &androidx.compose.material.icons.filled  VoiceControlApp &androidx.compose.material.icons.filled  VoiceControlAppTheme &androidx.compose.material.icons.filled  VoiceScreen &androidx.compose.material.icons.filled  Wifi &androidx.compose.material.icons.filled  WifiOff &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  buttonColors &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  takeLast &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  ActionButton androidx.compose.material3  ActionControlArea androidx.compose.material3  	Alignment androidx.compose.material3  ApiEndpointItem androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  CameraScreen androidx.compose.material3  CameraSettings androidx.compose.material3  CameraVideoArea androidx.compose.material3  CameraViewModel androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  CommandHistoryCard androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ConnectionStatusCard androidx.compose.material3  Context androidx.compose.material3  
ControlButton androidx.compose.material3  
ControlScreen androidx.compose.material3  ControlViewModel androidx.compose.material3  DirectionControlArea androidx.compose.material3  
ExpandLess androidx.compose.material3  
ExpandMore androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExperimentalPermissionsApi androidx.compose.material3  
FontFamily androidx.compose.material3  
FontWeight androidx.compose.material3  Home androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  	ImeAction androidx.compose.material3  Int androidx.compose.material3  KeyboardActions androidx.compose.material3  KeyboardArrowDown androidx.compose.material3  KeyboardArrowLeft androidx.compose.material3  KeyboardArrowRight androidx.compose.material3  KeyboardArrowUp androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  Log androidx.compose.material3  MainContent androidx.compose.material3  
MainViewModel androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  Mic androidx.compose.material3  MicOff androidx.compose.material3  Modifier androidx.compose.material3  
NavController androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PermissionRequestCard androidx.compose.material3  Pets androidx.compose.material3  	PlayArrow androidx.compose.material3  Preview androidx.compose.material3  Refresh androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Screen androidx.compose.material3  Settings androidx.compose.material3  SettingsScreen androidx.compose.material3  Spacer androidx.compose.material3  	StatusBar androidx.compose.material3  StatusIndicator androidx.compose.material3  
StatusMessage androidx.compose.material3  Stop androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  	TopAppBar androidx.compose.material3  TouchApp androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  UsageInstructions androidx.compose.material3  Videocam androidx.compose.material3  VoiceButton androidx.compose.material3  VoiceControlApp androidx.compose.material3  VoiceControlAppTheme androidx.compose.material3  VoiceScreen androidx.compose.material3  Wifi androidx.compose.material3  WifiOff androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  clip androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  launch androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  takeLast androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSecondaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  onTertiaryContainer &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  tertiaryContainer &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  ActionButton androidx.compose.runtime  ActionControlArea androidx.compose.runtime  	Alignment androidx.compose.runtime  ApiEndpointItem androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  CameraScreen androidx.compose.runtime  CameraSettings androidx.compose.runtime  CameraVideoArea androidx.compose.runtime  CameraViewModel androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  CommandHistoryCard androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ConnectionStatusCard androidx.compose.runtime  Context androidx.compose.runtime  
ControlButton androidx.compose.runtime  
ControlScreen androidx.compose.runtime  ControlViewModel androidx.compose.runtime  DirectionControlArea androidx.compose.runtime  
ExpandLess androidx.compose.runtime  
ExpandMore androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExperimentalPermissionsApi androidx.compose.runtime  
FontFamily androidx.compose.runtime  
FontWeight androidx.compose.runtime  Home androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  	ImeAction androidx.compose.runtime  Int androidx.compose.runtime  KeyboardActions androidx.compose.runtime  KeyboardArrowDown androidx.compose.runtime  KeyboardArrowLeft androidx.compose.runtime  KeyboardArrowRight androidx.compose.runtime  KeyboardArrowUp androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  List androidx.compose.runtime  Log androidx.compose.runtime  MainContent androidx.compose.runtime  
MainViewModel androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Mic androidx.compose.runtime  MicOff androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  
NavController androidx.compose.runtime  
NavigationBar androidx.compose.runtime  NavigationBarItem androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PermissionRequestCard androidx.compose.runtime  Pets androidx.compose.runtime  	PlayArrow androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Refresh androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Screen androidx.compose.runtime  Settings androidx.compose.runtime  SettingsScreen androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  	StatusBar androidx.compose.runtime  StatusIndicator androidx.compose.runtime  
StatusMessage androidx.compose.runtime  Stop androidx.compose.runtime  String androidx.compose.runtime  Surface androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TouchApp androidx.compose.runtime  Unit androidx.compose.runtime  UsageInstructions androidx.compose.runtime  Videocam androidx.compose.runtime  VoiceButton androidx.compose.runtime  VoiceControlApp androidx.compose.runtime  VoiceControlAppTheme androidx.compose.runtime  VoiceScreen androidx.compose.runtime  Wifi androidx.compose.runtime  WifiOff androidx.compose.runtime  androidx androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  clip androidx.compose.runtime  delay androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  launch androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  takeLast androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  scale androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  AwaitPointerEventScope !androidx.compose.ui.input.pointer  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  Log 8androidx.compose.ui.input.pointer.AwaitPointerEventScope  awaitFirstDown 8androidx.compose.ui.input.pointer.AwaitPointerEventScope  waitForUpOrCancellation 8androidx.compose.ui.input.pointer.AwaitPointerEventScope  Log 3androidx.compose.ui.input.pointer.PointerInputScope  awaitEachGesture 3androidx.compose.ui.input.pointer.PointerInputScope  awaitFirstDown 3androidx.compose.ui.input.pointer.PointerInputScope  detectTapGestures 3androidx.compose.ui.input.pointer.PointerInputScope  waitForUpOrCancellation 3androidx.compose.ui.input.pointer.PointerInputScope  ContentScale androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  SoftwareKeyboardController androidx.compose.ui.platform  hide 7androidx.compose.ui.platform.SoftwareKeyboardController  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  KeyboardType androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Done (androidx.compose.ui.text.input.ImeAction  Done 2androidx.compose.ui.text.input.ImeAction.Companion  	Companion +androidx.compose.ui.text.input.KeyboardType  Uri +androidx.compose.ui.text.input.KeyboardType  Uri 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  VoiceControlApp #androidx.core.app.ComponentActivity  VoiceControlAppTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  	onCleared androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  NavBackStackEntry androidx.navigation  
NavController androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  currentBackStackEntryAsState !androidx.navigation.NavController  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  route "androidx.navigation.NavDestination  startDestinationId androidx.navigation.NavGraph  CameraScreen #androidx.navigation.NavGraphBuilder  
ControlScreen #androidx.navigation.NavGraphBuilder  Screen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  VoiceScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  ActionButton com.example.voicecontrolapp  ActionControlArea com.example.voicecontrolapp  	Alignment com.example.voicecontrolapp  ApiEndpointItem com.example.voicecontrolapp  Arrangement com.example.voicecontrolapp  Boolean com.example.voicecontrolapp  BottomNavigationBar com.example.voicecontrolapp  Box com.example.voicecontrolapp  Build com.example.voicecontrolapp  Bundle com.example.voicecontrolapp  Button com.example.voicecontrolapp  ButtonDefaults com.example.voicecontrolapp  CONTROL_PATH com.example.voicecontrolapp  CameraScreen com.example.voicecontrolapp  CameraSettings com.example.voicecontrolapp  CameraVideoArea com.example.voicecontrolapp  CameraViewModel com.example.voicecontrolapp  Card com.example.voicecontrolapp  CardDefaults com.example.voicecontrolapp  CircleShape com.example.voicecontrolapp  CircularProgressIndicator com.example.voicecontrolapp  Color com.example.voicecontrolapp  Column com.example.voicecontrolapp  CommandHistoryCard com.example.voicecontrolapp  ComponentActivity com.example.voicecontrolapp  
Composable com.example.voicecontrolapp  
ConfigManager com.example.voicecontrolapp  ConnectionStatusCard com.example.voicecontrolapp  Context com.example.voicecontrolapp  
ControlButton com.example.voicecontrolapp  
ControlScreen com.example.voicecontrolapp  ControlViewModel com.example.voicecontrolapp  DEFAULT_CAMERA_URL com.example.voicecontrolapp  DEFAULT_RASPBERRY_PI_URL com.example.voicecontrolapp  Date com.example.voicecontrolapp  DirectionControlArea com.example.voicecontrolapp  Dispatchers com.example.voicecontrolapp  	Exception com.example.voicecontrolapp  
ExpandLess com.example.voicecontrolapp  
ExpandMore com.example.voicecontrolapp  ExperimentalMaterial3Api com.example.voicecontrolapp  ExperimentalPermissionsApi com.example.voicecontrolapp  File com.example.voicecontrolapp  
FontFamily com.example.voicecontrolapp  
FontWeight com.example.voicecontrolapp  Home com.example.voicecontrolapp  IOException com.example.voicecontrolapp  Icon com.example.voicecontrolapp  
IconButton com.example.voicecontrolapp  Icons com.example.voicecontrolapp  ImageVector com.example.voicecontrolapp  	ImeAction com.example.voicecontrolapp  Int com.example.voicecontrolapp  Job com.example.voicecontrolapp  KEY_CAMERA_URL com.example.voicecontrolapp  KEY_RASPBERRY_PI_URL com.example.voicecontrolapp  KeyboardActions com.example.voicecontrolapp  KeyboardArrowDown com.example.voicecontrolapp  KeyboardArrowLeft com.example.voicecontrolapp  KeyboardArrowRight com.example.voicecontrolapp  KeyboardArrowUp com.example.voicecontrolapp  KeyboardOptions com.example.voicecontrolapp  KeyboardType com.example.voicecontrolapp  LaunchedEffect com.example.voicecontrolapp  LinearProgressIndicator com.example.voicecontrolapp  List com.example.voicecontrolapp  Locale com.example.voicecontrolapp  Log com.example.voicecontrolapp  MainActivity com.example.voicecontrolapp  MainContent com.example.voicecontrolapp  
MainViewModel com.example.voicecontrolapp  Manifest com.example.voicecontrolapp  Map com.example.voicecontrolapp  
MaterialTheme com.example.voicecontrolapp  
MediaRecorder com.example.voicecontrolapp  Mic com.example.voicecontrolapp  MicOff com.example.voicecontrolapp  Modifier com.example.voicecontrolapp  
MultipartBody com.example.voicecontrolapp  MutableStateFlow com.example.voicecontrolapp  
NavController com.example.voicecontrolapp  
NavigationBar com.example.voicecontrolapp  NavigationBarItem com.example.voicecontrolapp  NetworkManager com.example.voicecontrolapp  
NetworkResult com.example.voicecontrolapp  OkHttpClient com.example.voicecontrolapp  OptIn com.example.voicecontrolapp  OutlinedButton com.example.voicecontrolapp  OutlinedTextField com.example.voicecontrolapp  	PING_PATH com.example.voicecontrolapp  
PREFS_NAME com.example.voicecontrolapp  PermissionRequestCard com.example.voicecontrolapp  Pets com.example.voicecontrolapp  	PlayArrow com.example.voicecontrolapp  Preview com.example.voicecontrolapp  Refresh com.example.voicecontrolapp  Request com.example.voicecontrolapp  RoundedCornerShape com.example.voicecontrolapp  Row com.example.voicecontrolapp  Scaffold com.example.voicecontrolapp  Screen com.example.voicecontrolapp  Settings com.example.voicecontrolapp  SettingsScreen com.example.voicecontrolapp  SharedPreferences com.example.voicecontrolapp  SimpleDateFormat com.example.voicecontrolapp  Spacer com.example.voicecontrolapp  State com.example.voicecontrolapp  	StateFlow com.example.voicecontrolapp  	StatusBar com.example.voicecontrolapp  StatusIndicator com.example.voicecontrolapp  
StatusMessage com.example.voicecontrolapp  Stop com.example.voicecontrolapp  String com.example.voicecontrolapp  Suppress com.example.voicecontrolapp  Surface com.example.voicecontrolapp  System com.example.voicecontrolapp  TAG com.example.voicecontrolapp  Text com.example.voicecontrolapp  	TextAlign com.example.voicecontrolapp  TimeUnit com.example.voicecontrolapp  	TopAppBar com.example.voicecontrolapp  TouchApp com.example.voicecontrolapp  UPLOAD_PATH com.example.voicecontrolapp  Unit com.example.voicecontrolapp  UsageInstructions com.example.voicecontrolapp  Videocam com.example.voicecontrolapp  	ViewModel com.example.voicecontrolapp  VoiceButton com.example.voicecontrolapp  VoiceControlApp com.example.voicecontrolapp  VoiceControlAppPreview com.example.voicecontrolapp  VoiceControlAppTheme com.example.voicecontrolapp  VoiceRecorderManager com.example.voicecontrolapp  VoiceScreen com.example.voicecontrolapp  Volatile com.example.voicecontrolapp  Wifi com.example.voicecontrolapp  WifiOff com.example.voicecontrolapp  
_cameraUrl com.example.voicecontrolapp  _commandHistory com.example.voicecontrolapp  _connectionStatus com.example.voicecontrolapp  
_errorMessage com.example.voicecontrolapp  _isConnected com.example.voicecontrolapp  _isRecording com.example.voicecontrolapp  _isStreaming com.example.voicecontrolapp  _lastCommand com.example.voicecontrolapp  _raspberryPiUrl com.example.voicecontrolapp  	_robotUrl com.example.voicecontrolapp  _statusMessage com.example.voicecontrolapp  addToHistory com.example.voicecontrolapp  also com.example.voicecontrolapp  androidx com.example.voicecontrolapp  
appendLine com.example.voicecontrolapp  apply com.example.voicecontrolapp  
asRequestBody com.example.voicecontrolapp  asStateFlow com.example.voicecontrolapp  
background com.example.voicecontrolapp  buildString com.example.voicecontrolapp  buttonColors com.example.voicecontrolapp  
cardColors com.example.voicecontrolapp  checkConnection com.example.voicecontrolapp  checkStreamAvailability com.example.voicecontrolapp  client com.example.voicecontrolapp  clip com.example.voicecontrolapp  
configManager com.example.voicecontrolapp  contains com.example.voicecontrolapp  
dateFormat com.example.voicecontrolapp  delay com.example.voicecontrolapp  	emptyList com.example.voicecontrolapp  extractBaseUrl com.example.voicecontrolapp  fillMaxSize com.example.voicecontrolapp  fillMaxWidth com.example.voicecontrolapp  forEach com.example.voicecontrolapp  	formatUrl com.example.voicecontrolapp  getCurrentCameraUrl com.example.voicecontrolapp  getCurrentRaspberryPiUrl com.example.voicecontrolapp  getInstance com.example.voicecontrolapp  getValue com.example.voicecontrolapp  height com.example.voicecontrolapp  isEmpty com.example.voicecontrolapp  
isInitialized com.example.voicecontrolapp  
isNotBlank com.example.voicecontrolapp  
isNotEmpty com.example.voicecontrolapp  isRecording com.example.voicecontrolapp  java com.example.voicecontrolapp  launch com.example.voicecontrolapp  launchIn com.example.voicecontrolapp  let com.example.voicecontrolapp  listOf com.example.voicecontrolapp  mapOf com.example.voicecontrolapp  mutableStateOf com.example.voicecontrolapp  networkManager com.example.voicecontrolapp  onEach com.example.voicecontrolapp  
outputFile com.example.voicecontrolapp  padding com.example.voicecontrolapp  provideDelegate com.example.voicecontrolapp  remember com.example.voicecontrolapp  rememberCoroutineScope com.example.voicecontrolapp  repeat com.example.voicecontrolapp  
sendAudioFile com.example.voicecontrolapp  sendCommand com.example.voicecontrolapp  sendCommandToRobot com.example.voicecontrolapp  setValue com.example.voicecontrolapp  size com.example.voicecontrolapp  spacedBy com.example.voicecontrolapp  split com.example.voicecontrolapp  startStream com.example.voicecontrolapp  
startsWith com.example.voicecontrolapp  
stopStream com.example.voicecontrolapp  synchronized com.example.voicecontrolapp  takeLast com.example.voicecontrolapp  to com.example.voicecontrolapp  toIntOrNull com.example.voicecontrolapp  toMediaType com.example.voicecontrolapp  
toMutableList com.example.voicecontrolapp  trim com.example.voicecontrolapp  trimEnd com.example.voicecontrolapp  	trimStart com.example.voicecontrolapp  voiceRecorderManager com.example.voicecontrolapp  weight com.example.voicecontrolapp  width com.example.voicecontrolapp  withContext com.example.voicecontrolapp  Boolean +com.example.voicecontrolapp.CameraViewModel  
ConfigManager +com.example.voicecontrolapp.CameraViewModel  Context +com.example.voicecontrolapp.CameraViewModel  	Exception +com.example.voicecontrolapp.CameraViewModel  Job +com.example.voicecontrolapp.CameraViewModel  Log +com.example.voicecontrolapp.CameraViewModel  State +com.example.voicecontrolapp.CameraViewModel  String +com.example.voicecontrolapp.CameraViewModel  TAG +com.example.voicecontrolapp.CameraViewModel  
_cameraUrl +com.example.voicecontrolapp.CameraViewModel  
_errorMessage +com.example.voicecontrolapp.CameraViewModel  _isConnected +com.example.voicecontrolapp.CameraViewModel  _isStreaming +com.example.voicecontrolapp.CameraViewModel  	cameraUrl +com.example.voicecontrolapp.CameraViewModel  checkConnection +com.example.voicecontrolapp.CameraViewModel  checkStreamAvailability +com.example.voicecontrolapp.CameraViewModel  
configManager +com.example.voicecontrolapp.CameraViewModel  connectionCheckJob +com.example.voicecontrolapp.CameraViewModel  delay +com.example.voicecontrolapp.CameraViewModel  errorMessage +com.example.voicecontrolapp.CameraViewModel  extractBaseUrl +com.example.voicecontrolapp.CameraViewModel  getInstance +com.example.voicecontrolapp.CameraViewModel  
initialize +com.example.voicecontrolapp.CameraViewModel  invoke +com.example.voicecontrolapp.CameraViewModel  isConnected +com.example.voicecontrolapp.CameraViewModel  isEmpty +com.example.voicecontrolapp.CameraViewModel  
isInitialized +com.example.voicecontrolapp.CameraViewModel  
isNotEmpty +com.example.voicecontrolapp.CameraViewModel  isStreaming +com.example.voicecontrolapp.CameraViewModel  launch +com.example.voicecontrolapp.CameraViewModel  launchIn +com.example.voicecontrolapp.CameraViewModel  mutableStateOf +com.example.voicecontrolapp.CameraViewModel  onEach +com.example.voicecontrolapp.CameraViewModel  
refreshCamera +com.example.voicecontrolapp.CameraViewModel  split +com.example.voicecontrolapp.CameraViewModel  startConnectionMonitoring +com.example.voicecontrolapp.CameraViewModel  startStream +com.example.voicecontrolapp.CameraViewModel  
startsWith +com.example.voicecontrolapp.CameraViewModel  
stopStream +com.example.voicecontrolapp.CameraViewModel  	streamJob +com.example.voicecontrolapp.CameraViewModel  updateCameraUrl +com.example.voicecontrolapp.CameraViewModel  viewModelScope +com.example.voicecontrolapp.CameraViewModel  
ConfigManager 5com.example.voicecontrolapp.CameraViewModel.Companion  	Exception 5com.example.voicecontrolapp.CameraViewModel.Companion  Log 5com.example.voicecontrolapp.CameraViewModel.Companion  TAG 5com.example.voicecontrolapp.CameraViewModel.Companion  
_cameraUrl 5com.example.voicecontrolapp.CameraViewModel.Companion  
_errorMessage 5com.example.voicecontrolapp.CameraViewModel.Companion  _isConnected 5com.example.voicecontrolapp.CameraViewModel.Companion  _isStreaming 5com.example.voicecontrolapp.CameraViewModel.Companion  checkConnection 5com.example.voicecontrolapp.CameraViewModel.Companion  checkStreamAvailability 5com.example.voicecontrolapp.CameraViewModel.Companion  
configManager 5com.example.voicecontrolapp.CameraViewModel.Companion  delay 5com.example.voicecontrolapp.CameraViewModel.Companion  extractBaseUrl 5com.example.voicecontrolapp.CameraViewModel.Companion  getInstance 5com.example.voicecontrolapp.CameraViewModel.Companion  isEmpty 5com.example.voicecontrolapp.CameraViewModel.Companion  
isInitialized 5com.example.voicecontrolapp.CameraViewModel.Companion  
isNotEmpty 5com.example.voicecontrolapp.CameraViewModel.Companion  launch 5com.example.voicecontrolapp.CameraViewModel.Companion  launchIn 5com.example.voicecontrolapp.CameraViewModel.Companion  mutableStateOf 5com.example.voicecontrolapp.CameraViewModel.Companion  onEach 5com.example.voicecontrolapp.CameraViewModel.Companion  split 5com.example.voicecontrolapp.CameraViewModel.Companion  startStream 5com.example.voicecontrolapp.CameraViewModel.Companion  
startsWith 5com.example.voicecontrolapp.CameraViewModel.Companion  
stopStream 5com.example.voicecontrolapp.CameraViewModel.Companion  viewModelScope 5com.example.voicecontrolapp.CameraViewModel.Companion  Boolean )com.example.voicecontrolapp.ConfigManager  	Companion )com.example.voicecontrolapp.ConfigManager  
ConfigManager )com.example.voicecontrolapp.ConfigManager  Context )com.example.voicecontrolapp.ConfigManager  DEFAULT_CAMERA_URL )com.example.voicecontrolapp.ConfigManager  DEFAULT_RASPBERRY_PI_URL )com.example.voicecontrolapp.ConfigManager  	Exception )com.example.voicecontrolapp.ConfigManager  INSTANCE )com.example.voicecontrolapp.ConfigManager  KEY_CAMERA_URL )com.example.voicecontrolapp.ConfigManager  KEY_RASPBERRY_PI_URL )com.example.voicecontrolapp.ConfigManager  Log )com.example.voicecontrolapp.ConfigManager  Map )com.example.voicecontrolapp.ConfigManager  MutableStateFlow )com.example.voicecontrolapp.ConfigManager  
PREFS_NAME )com.example.voicecontrolapp.ConfigManager  SharedPreferences )com.example.voicecontrolapp.ConfigManager  	StateFlow )com.example.voicecontrolapp.ConfigManager  String )com.example.voicecontrolapp.ConfigManager  System )com.example.voicecontrolapp.ConfigManager  TAG )com.example.voicecontrolapp.ConfigManager  Volatile )com.example.voicecontrolapp.ConfigManager  
_cameraUrl )com.example.voicecontrolapp.ConfigManager  _raspberryPiUrl )com.example.voicecontrolapp.ConfigManager  also )com.example.voicecontrolapp.ConfigManager  
appendLine )com.example.voicecontrolapp.ConfigManager  asStateFlow )com.example.voicecontrolapp.ConfigManager  buildString )com.example.voicecontrolapp.ConfigManager  	cameraUrl )com.example.voicecontrolapp.ConfigManager  contains )com.example.voicecontrolapp.ConfigManager  context )com.example.voicecontrolapp.ConfigManager  generateDefaultCameraUrl )com.example.voicecontrolapp.ConfigManager  getCurrentCameraUrl )com.example.voicecontrolapp.ConfigManager  getCurrentRaspberryPiUrl )com.example.voicecontrolapp.ConfigManager  getDefaultCameraUrl )com.example.voicecontrolapp.ConfigManager  getDefaultRaspberryPiUrl )com.example.voicecontrolapp.ConfigManager  getInstance )com.example.voicecontrolapp.ConfigManager  isEmpty )com.example.voicecontrolapp.ConfigManager  let )com.example.voicecontrolapp.ConfigManager  loadConfiguration )com.example.voicecontrolapp.ConfigManager  mapOf )com.example.voicecontrolapp.ConfigManager  raspberryPiUrl )com.example.voicecontrolapp.ConfigManager  resetToDefaults )com.example.voicecontrolapp.ConfigManager  sharedPreferences )com.example.voicecontrolapp.ConfigManager  split )com.example.voicecontrolapp.ConfigManager  
startsWith )com.example.voicecontrolapp.ConfigManager  synchronized )com.example.voicecontrolapp.ConfigManager  to )com.example.voicecontrolapp.ConfigManager  toIntOrNull )com.example.voicecontrolapp.ConfigManager  trim )com.example.voicecontrolapp.ConfigManager  updateCameraUrl )com.example.voicecontrolapp.ConfigManager  updateCameraUrlInternal )com.example.voicecontrolapp.ConfigManager  updateRaspberryPiUrl )com.example.voicecontrolapp.ConfigManager  
ConfigManager 3com.example.voicecontrolapp.ConfigManager.Companion  Context 3com.example.voicecontrolapp.ConfigManager.Companion  DEFAULT_CAMERA_URL 3com.example.voicecontrolapp.ConfigManager.Companion  DEFAULT_RASPBERRY_PI_URL 3com.example.voicecontrolapp.ConfigManager.Companion  INSTANCE 3com.example.voicecontrolapp.ConfigManager.Companion  KEY_CAMERA_URL 3com.example.voicecontrolapp.ConfigManager.Companion  KEY_RASPBERRY_PI_URL 3com.example.voicecontrolapp.ConfigManager.Companion  Log 3com.example.voicecontrolapp.ConfigManager.Companion  MutableStateFlow 3com.example.voicecontrolapp.ConfigManager.Companion  
PREFS_NAME 3com.example.voicecontrolapp.ConfigManager.Companion  System 3com.example.voicecontrolapp.ConfigManager.Companion  TAG 3com.example.voicecontrolapp.ConfigManager.Companion  also 3com.example.voicecontrolapp.ConfigManager.Companion  
appendLine 3com.example.voicecontrolapp.ConfigManager.Companion  asStateFlow 3com.example.voicecontrolapp.ConfigManager.Companion  buildString 3com.example.voicecontrolapp.ConfigManager.Companion  contains 3com.example.voicecontrolapp.ConfigManager.Companion  getCurrentCameraUrl 3com.example.voicecontrolapp.ConfigManager.Companion  getCurrentRaspberryPiUrl 3com.example.voicecontrolapp.ConfigManager.Companion  getInstance 3com.example.voicecontrolapp.ConfigManager.Companion  isEmpty 3com.example.voicecontrolapp.ConfigManager.Companion  let 3com.example.voicecontrolapp.ConfigManager.Companion  mapOf 3com.example.voicecontrolapp.ConfigManager.Companion  split 3com.example.voicecontrolapp.ConfigManager.Companion  
startsWith 3com.example.voicecontrolapp.ConfigManager.Companion  synchronized 3com.example.voicecontrolapp.ConfigManager.Companion  to 3com.example.voicecontrolapp.ConfigManager.Companion  toIntOrNull 3com.example.voicecontrolapp.ConfigManager.Companion  trim 3com.example.voicecontrolapp.ConfigManager.Companion  Boolean ,com.example.voicecontrolapp.ControlViewModel  CONTROL_PATH ,com.example.voicecontrolapp.ControlViewModel  
ConfigManager ,com.example.voicecontrolapp.ControlViewModel  Context ,com.example.voicecontrolapp.ControlViewModel  Date ,com.example.voicecontrolapp.ControlViewModel  	Exception ,com.example.voicecontrolapp.ControlViewModel  Job ,com.example.voicecontrolapp.ControlViewModel  List ,com.example.voicecontrolapp.ControlViewModel  Locale ,com.example.voicecontrolapp.ControlViewModel  Log ,com.example.voicecontrolapp.ControlViewModel  NetworkManager ,com.example.voicecontrolapp.ControlViewModel  SimpleDateFormat ,com.example.voicecontrolapp.ControlViewModel  State ,com.example.voicecontrolapp.ControlViewModel  String ,com.example.voicecontrolapp.ControlViewModel  System ,com.example.voicecontrolapp.ControlViewModel  TAG ,com.example.voicecontrolapp.ControlViewModel  _commandHistory ,com.example.voicecontrolapp.ControlViewModel  _connectionStatus ,com.example.voicecontrolapp.ControlViewModel  _isConnected ,com.example.voicecontrolapp.ControlViewModel  _lastCommand ,com.example.voicecontrolapp.ControlViewModel  	_robotUrl ,com.example.voicecontrolapp.ControlViewModel  addToHistory ,com.example.voicecontrolapp.ControlViewModel  
appendLine ,com.example.voicecontrolapp.ControlViewModel  buildString ,com.example.voicecontrolapp.ControlViewModel  checkConnection ,com.example.voicecontrolapp.ControlViewModel  commandHistory ,com.example.voicecontrolapp.ControlViewModel  
configManager ,com.example.voicecontrolapp.ControlViewModel  connectionCheckJob ,com.example.voicecontrolapp.ControlViewModel  connectionStatus ,com.example.voicecontrolapp.ControlViewModel  
dateFormat ,com.example.voicecontrolapp.ControlViewModel  delay ,com.example.voicecontrolapp.ControlViewModel  	emptyList ,com.example.voicecontrolapp.ControlViewModel  getInstance ,com.example.voicecontrolapp.ControlViewModel  
initialize ,com.example.voicecontrolapp.ControlViewModel  invoke ,com.example.voicecontrolapp.ControlViewModel  isConnected ,com.example.voicecontrolapp.ControlViewModel  
isInitialized ,com.example.voicecontrolapp.ControlViewModel  lastCommand ,com.example.voicecontrolapp.ControlViewModel  launch ,com.example.voicecontrolapp.ControlViewModel  launchIn ,com.example.voicecontrolapp.ControlViewModel  listOf ,com.example.voicecontrolapp.ControlViewModel  mapOf ,com.example.voicecontrolapp.ControlViewModel  mutableStateOf ,com.example.voicecontrolapp.ControlViewModel  networkManager ,com.example.voicecontrolapp.ControlViewModel  onEach ,com.example.voicecontrolapp.ControlViewModel  repeat ,com.example.voicecontrolapp.ControlViewModel  sendCommand ,com.example.voicecontrolapp.ControlViewModel  sendCommandToRobot ,com.example.voicecontrolapp.ControlViewModel  startConnectionMonitoring ,com.example.voicecontrolapp.ControlViewModel  to ,com.example.voicecontrolapp.ControlViewModel  
toMutableList ,com.example.voicecontrolapp.ControlViewModel  viewModelScope ,com.example.voicecontrolapp.ControlViewModel  CONTROL_PATH 6com.example.voicecontrolapp.ControlViewModel.Companion  
ConfigManager 6com.example.voicecontrolapp.ControlViewModel.Companion  Date 6com.example.voicecontrolapp.ControlViewModel.Companion  Locale 6com.example.voicecontrolapp.ControlViewModel.Companion  Log 6com.example.voicecontrolapp.ControlViewModel.Companion  NetworkManager 6com.example.voicecontrolapp.ControlViewModel.Companion  SimpleDateFormat 6com.example.voicecontrolapp.ControlViewModel.Companion  System 6com.example.voicecontrolapp.ControlViewModel.Companion  TAG 6com.example.voicecontrolapp.ControlViewModel.Companion  _commandHistory 6com.example.voicecontrolapp.ControlViewModel.Companion  _connectionStatus 6com.example.voicecontrolapp.ControlViewModel.Companion  _isConnected 6com.example.voicecontrolapp.ControlViewModel.Companion  _lastCommand 6com.example.voicecontrolapp.ControlViewModel.Companion  	_robotUrl 6com.example.voicecontrolapp.ControlViewModel.Companion  addToHistory 6com.example.voicecontrolapp.ControlViewModel.Companion  
appendLine 6com.example.voicecontrolapp.ControlViewModel.Companion  buildString 6com.example.voicecontrolapp.ControlViewModel.Companion  checkConnection 6com.example.voicecontrolapp.ControlViewModel.Companion  
configManager 6com.example.voicecontrolapp.ControlViewModel.Companion  
dateFormat 6com.example.voicecontrolapp.ControlViewModel.Companion  delay 6com.example.voicecontrolapp.ControlViewModel.Companion  	emptyList 6com.example.voicecontrolapp.ControlViewModel.Companion  getInstance 6com.example.voicecontrolapp.ControlViewModel.Companion  
isInitialized 6com.example.voicecontrolapp.ControlViewModel.Companion  launch 6com.example.voicecontrolapp.ControlViewModel.Companion  launchIn 6com.example.voicecontrolapp.ControlViewModel.Companion  listOf 6com.example.voicecontrolapp.ControlViewModel.Companion  mapOf 6com.example.voicecontrolapp.ControlViewModel.Companion  mutableStateOf 6com.example.voicecontrolapp.ControlViewModel.Companion  networkManager 6com.example.voicecontrolapp.ControlViewModel.Companion  onEach 6com.example.voicecontrolapp.ControlViewModel.Companion  repeat 6com.example.voicecontrolapp.ControlViewModel.Companion  sendCommand 6com.example.voicecontrolapp.ControlViewModel.Companion  sendCommandToRobot 6com.example.voicecontrolapp.ControlViewModel.Companion  to 6com.example.voicecontrolapp.ControlViewModel.Companion  
toMutableList 6com.example.voicecontrolapp.ControlViewModel.Companion  viewModelScope 6com.example.voicecontrolapp.ControlViewModel.Companion  VoiceControlApp (com.example.voicecontrolapp.MainActivity  VoiceControlAppTheme (com.example.voicecontrolapp.MainActivity  enableEdgeToEdge (com.example.voicecontrolapp.MainActivity  getValue (com.example.voicecontrolapp.MainActivity  
mainViewModel (com.example.voicecontrolapp.MainActivity  provideDelegate (com.example.voicecontrolapp.MainActivity  
setContent (com.example.voicecontrolapp.MainActivity  
viewModels (com.example.voicecontrolapp.MainActivity  Boolean )com.example.voicecontrolapp.MainViewModel  
ConfigManager )com.example.voicecontrolapp.MainViewModel  Context )com.example.voicecontrolapp.MainViewModel  	Exception )com.example.voicecontrolapp.MainViewModel  File )com.example.voicecontrolapp.MainViewModel  Int )com.example.voicecontrolapp.MainViewModel  Job )com.example.voicecontrolapp.MainViewModel  Log )com.example.voicecontrolapp.MainViewModel  NetworkManager )com.example.voicecontrolapp.MainViewModel  
NetworkResult )com.example.voicecontrolapp.MainViewModel  State )com.example.voicecontrolapp.MainViewModel  String )com.example.voicecontrolapp.MainViewModel  TAG )com.example.voicecontrolapp.MainViewModel  VoiceRecorderManager )com.example.voicecontrolapp.MainViewModel  _hasRecordPermission )com.example.voicecontrolapp.MainViewModel  _isConnected )com.example.voicecontrolapp.MainViewModel  _isRecording )com.example.voicecontrolapp.MainViewModel  
_isSending )com.example.voicecontrolapp.MainViewModel  _raspberryPiUrl )com.example.voicecontrolapp.MainViewModel  _statusMessage )com.example.voicecontrolapp.MainViewModel  _uploadProgress )com.example.voicecontrolapp.MainViewModel  checkConnection )com.example.voicecontrolapp.MainViewModel  
configManager )com.example.voicecontrolapp.MainViewModel  connectionCheckJob )com.example.voicecontrolapp.MainViewModel  delay )com.example.voicecontrolapp.MainViewModel  forceStopRecording )com.example.voicecontrolapp.MainViewModel  getInstance )com.example.voicecontrolapp.MainViewModel  
initialize )com.example.voicecontrolapp.MainViewModel  isConnected )com.example.voicecontrolapp.MainViewModel  
isInitialized )com.example.voicecontrolapp.MainViewModel  isRecording )com.example.voicecontrolapp.MainViewModel  	isSending )com.example.voicecontrolapp.MainViewModel  launch )com.example.voicecontrolapp.MainViewModel  launchIn )com.example.voicecontrolapp.MainViewModel  mutableStateOf )com.example.voicecontrolapp.MainViewModel  networkManager )com.example.voicecontrolapp.MainViewModel  onEach )com.example.voicecontrolapp.MainViewModel  raspberryPiUrl )com.example.voicecontrolapp.MainViewModel  refreshConnection )com.example.voicecontrolapp.MainViewModel  
sendAudioFile )com.example.voicecontrolapp.MainViewModel  setRecordPermission )com.example.voicecontrolapp.MainViewModel  startConnectionMonitoring )com.example.voicecontrolapp.MainViewModel  startRecording )com.example.voicecontrolapp.MainViewModel  
statusMessage )com.example.voicecontrolapp.MainViewModel  stopRecordingAndSend )com.example.voicecontrolapp.MainViewModel  updateRaspberryPiUrl )com.example.voicecontrolapp.MainViewModel  uploadProgress )com.example.voicecontrolapp.MainViewModel  viewModelScope )com.example.voicecontrolapp.MainViewModel  voiceRecorderManager )com.example.voicecontrolapp.MainViewModel  
ConfigManager 3com.example.voicecontrolapp.MainViewModel.Companion  File 3com.example.voicecontrolapp.MainViewModel.Companion  Log 3com.example.voicecontrolapp.MainViewModel.Companion  NetworkManager 3com.example.voicecontrolapp.MainViewModel.Companion  TAG 3com.example.voicecontrolapp.MainViewModel.Companion  VoiceRecorderManager 3com.example.voicecontrolapp.MainViewModel.Companion  _isConnected 3com.example.voicecontrolapp.MainViewModel.Companion  _isRecording 3com.example.voicecontrolapp.MainViewModel.Companion  _raspberryPiUrl 3com.example.voicecontrolapp.MainViewModel.Companion  _statusMessage 3com.example.voicecontrolapp.MainViewModel.Companion  checkConnection 3com.example.voicecontrolapp.MainViewModel.Companion  
configManager 3com.example.voicecontrolapp.MainViewModel.Companion  delay 3com.example.voicecontrolapp.MainViewModel.Companion  getInstance 3com.example.voicecontrolapp.MainViewModel.Companion  
isInitialized 3com.example.voicecontrolapp.MainViewModel.Companion  launch 3com.example.voicecontrolapp.MainViewModel.Companion  launchIn 3com.example.voicecontrolapp.MainViewModel.Companion  mutableStateOf 3com.example.voicecontrolapp.MainViewModel.Companion  networkManager 3com.example.voicecontrolapp.MainViewModel.Companion  onEach 3com.example.voicecontrolapp.MainViewModel.Companion  
sendAudioFile 3com.example.voicecontrolapp.MainViewModel.Companion  viewModelScope 3com.example.voicecontrolapp.MainViewModel.Companion  voiceRecorderManager 3com.example.voicecontrolapp.MainViewModel.Companion  Error 7com.example.voicecontrolapp.MainViewModel.NetworkResult  Success 7com.example.voicecontrolapp.MainViewModel.NetworkResult  Boolean *com.example.voicecontrolapp.NetworkManager  Dispatchers *com.example.voicecontrolapp.NetworkManager  	Exception *com.example.voicecontrolapp.NetworkManager  File *com.example.voicecontrolapp.NetworkManager  IOException *com.example.voicecontrolapp.NetworkManager  Int *com.example.voicecontrolapp.NetworkManager  Log *com.example.voicecontrolapp.NetworkManager  
MultipartBody *com.example.voicecontrolapp.NetworkManager  
NetworkResult *com.example.voicecontrolapp.NetworkManager  OkHttpClient *com.example.voicecontrolapp.NetworkManager  	PING_PATH *com.example.voicecontrolapp.NetworkManager  Request *com.example.voicecontrolapp.NetworkManager  String *com.example.voicecontrolapp.NetworkManager  System *com.example.voicecontrolapp.NetworkManager  TAG *com.example.voicecontrolapp.NetworkManager  TimeUnit *com.example.voicecontrolapp.NetworkManager  UPLOAD_PATH *com.example.voicecontrolapp.NetworkManager  Unit *com.example.voicecontrolapp.NetworkManager  
asRequestBody *com.example.voicecontrolapp.NetworkManager  checkConnection *com.example.voicecontrolapp.NetworkManager  cleanup *com.example.voicecontrolapp.NetworkManager  client *com.example.voicecontrolapp.NetworkManager  	formatUrl *com.example.voicecontrolapp.NetworkManager  java *com.example.voicecontrolapp.NetworkManager  sendVoiceToRaspberryPi *com.example.voicecontrolapp.NetworkManager  
startsWith *com.example.voicecontrolapp.NetworkManager  toMediaType *com.example.voicecontrolapp.NetworkManager  trimEnd *com.example.voicecontrolapp.NetworkManager  	trimStart *com.example.voicecontrolapp.NetworkManager  withContext *com.example.voicecontrolapp.NetworkManager  Dispatchers 4com.example.voicecontrolapp.NetworkManager.Companion  Log 4com.example.voicecontrolapp.NetworkManager.Companion  
MultipartBody 4com.example.voicecontrolapp.NetworkManager.Companion  
NetworkResult 4com.example.voicecontrolapp.NetworkManager.Companion  OkHttpClient 4com.example.voicecontrolapp.NetworkManager.Companion  	PING_PATH 4com.example.voicecontrolapp.NetworkManager.Companion  Request 4com.example.voicecontrolapp.NetworkManager.Companion  System 4com.example.voicecontrolapp.NetworkManager.Companion  TAG 4com.example.voicecontrolapp.NetworkManager.Companion  TimeUnit 4com.example.voicecontrolapp.NetworkManager.Companion  UPLOAD_PATH 4com.example.voicecontrolapp.NetworkManager.Companion  
asRequestBody 4com.example.voicecontrolapp.NetworkManager.Companion  client 4com.example.voicecontrolapp.NetworkManager.Companion  	formatUrl 4com.example.voicecontrolapp.NetworkManager.Companion  
startsWith 4com.example.voicecontrolapp.NetworkManager.Companion  toMediaType 4com.example.voicecontrolapp.NetworkManager.Companion  trimEnd 4com.example.voicecontrolapp.NetworkManager.Companion  	trimStart 4com.example.voicecontrolapp.NetworkManager.Companion  withContext 4com.example.voicecontrolapp.NetworkManager.Companion  net /com.example.voicecontrolapp.NetworkManager.java  ConnectException 3com.example.voicecontrolapp.NetworkManager.java.net  SocketTimeoutException 3com.example.voicecontrolapp.NetworkManager.java.net  UnknownHostException 3com.example.voicecontrolapp.NetworkManager.java.net  Error )com.example.voicecontrolapp.NetworkResult  
NetworkResult )com.example.voicecontrolapp.NetworkResult  String )com.example.voicecontrolapp.NetworkResult  Success )com.example.voicecontrolapp.NetworkResult  message /com.example.voicecontrolapp.NetworkResult.Error  data 1com.example.voicecontrolapp.NetworkResult.Success  Camera "com.example.voicecontrolapp.Screen  Control "com.example.voicecontrolapp.Screen  Icons "com.example.voicecontrolapp.Screen  Mic "com.example.voicecontrolapp.Screen  Settings "com.example.voicecontrolapp.Screen  TouchApp "com.example.voicecontrolapp.Screen  Videocam "com.example.voicecontrolapp.Screen  Voice "com.example.voicecontrolapp.Screen  icon "com.example.voicecontrolapp.Screen  route "com.example.voicecontrolapp.Screen  title "com.example.voicecontrolapp.Screen  values "com.example.voicecontrolapp.Screen  Boolean 0com.example.voicecontrolapp.VoiceRecorderManager  Build 0com.example.voicecontrolapp.VoiceRecorderManager  Context 0com.example.voicecontrolapp.VoiceRecorderManager  	Exception 0com.example.voicecontrolapp.VoiceRecorderManager  File 0com.example.voicecontrolapp.VoiceRecorderManager  IOException 0com.example.voicecontrolapp.VoiceRecorderManager  Log 0com.example.voicecontrolapp.VoiceRecorderManager  
MediaRecorder 0com.example.voicecontrolapp.VoiceRecorderManager  String 0com.example.voicecontrolapp.VoiceRecorderManager  Suppress 0com.example.voicecontrolapp.VoiceRecorderManager  System 0com.example.voicecontrolapp.VoiceRecorderManager  TAG 0com.example.voicecontrolapp.VoiceRecorderManager  apply 0com.example.voicecontrolapp.VoiceRecorderManager  cancelRecording 0com.example.voicecontrolapp.VoiceRecorderManager  context 0com.example.voicecontrolapp.VoiceRecorderManager  isRecording 0com.example.voicecontrolapp.VoiceRecorderManager  let 0com.example.voicecontrolapp.VoiceRecorderManager  
mediaRecorder 0com.example.voicecontrolapp.VoiceRecorderManager  
outputFile 0com.example.voicecontrolapp.VoiceRecorderManager  release 0com.example.voicecontrolapp.VoiceRecorderManager  startRecording 0com.example.voicecontrolapp.VoiceRecorderManager  
stopRecording 0com.example.voicecontrolapp.VoiceRecorderManager  Build :com.example.voicecontrolapp.VoiceRecorderManager.Companion  File :com.example.voicecontrolapp.VoiceRecorderManager.Companion  Log :com.example.voicecontrolapp.VoiceRecorderManager.Companion  
MediaRecorder :com.example.voicecontrolapp.VoiceRecorderManager.Companion  System :com.example.voicecontrolapp.VoiceRecorderManager.Companion  TAG :com.example.voicecontrolapp.VoiceRecorderManager.Companion  apply :com.example.voicecontrolapp.VoiceRecorderManager.Companion  isRecording :com.example.voicecontrolapp.VoiceRecorderManager.Companion  let :com.example.voicecontrolapp.VoiceRecorderManager.Companion  
outputFile :com.example.voicecontrolapp.VoiceRecorderManager.Companion  net  com.example.voicecontrolapp.java  ConnectException $com.example.voicecontrolapp.java.net  SocketTimeoutException $com.example.voicecontrolapp.java.net  UnknownHostException $com.example.voicecontrolapp.java.net  Boolean $com.example.voicecontrolapp.ui.theme  Build $com.example.voicecontrolapp.ui.theme  
Composable $com.example.voicecontrolapp.ui.theme  DarkColorScheme $com.example.voicecontrolapp.ui.theme  
FontFamily $com.example.voicecontrolapp.ui.theme  
FontWeight $com.example.voicecontrolapp.ui.theme  LightColorScheme $com.example.voicecontrolapp.ui.theme  Pink40 $com.example.voicecontrolapp.ui.theme  Pink80 $com.example.voicecontrolapp.ui.theme  Purple40 $com.example.voicecontrolapp.ui.theme  Purple80 $com.example.voicecontrolapp.ui.theme  PurpleGrey40 $com.example.voicecontrolapp.ui.theme  PurpleGrey80 $com.example.voicecontrolapp.ui.theme  
Typography $com.example.voicecontrolapp.ui.theme  Unit $com.example.voicecontrolapp.ui.theme  VoiceControlAppTheme $com.example.voicecontrolapp.ui.theme  ExperimentalPermissionsApi "com.google.accompanist.permissions  PermissionState "com.google.accompanist.permissions  PermissionStatus "com.google.accompanist.permissions  	isGranted "com.google.accompanist.permissions  rememberPermissionState "com.google.accompanist.permissions  shouldShowRationale "com.google.accompanist.permissions  launchPermissionRequest 2com.google.accompanist.permissions.PermissionState  status 2com.google.accompanist.permissions.PermissionState  	isGranted 3com.google.accompanist.permissions.PermissionStatus  shouldShowRationale 3com.google.accompanist.permissions.PermissionStatus  File java.io  IOException java.io  absolutePath java.io.File  
asRequestBody java.io.File  delete java.io.File  exists java.io.File  length java.io.File  mkdirs java.io.File  name java.io.File  message java.io.IOException  
Appendable 	java.lang  	Exception 	java.lang  
StringBuilder 	java.lang  message java.lang.Exception  
PREFS_NAME java.lang.StringBuilder  _commandHistory java.lang.StringBuilder  _isConnected java.lang.StringBuilder  _lastCommand java.lang.StringBuilder  
appendLine java.lang.StringBuilder  getCurrentCameraUrl java.lang.StringBuilder  getCurrentRaspberryPiUrl java.lang.StringBuilder  currentTimeMillis java.lang.System  ConnectException java.net  SocketTimeoutException java.net  UnknownHostException java.net  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Boolean 	java.util  CONTROL_PATH 	java.util  
ConfigManager 	java.util  Context 	java.util  Date 	java.util  	Exception 	java.util  Job 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  NetworkManager 	java.util  SimpleDateFormat 	java.util  State 	java.util  String 	java.util  System 	java.util  TAG 	java.util  	ViewModel 	java.util  _commandHistory 	java.util  _connectionStatus 	java.util  _isConnected 	java.util  _lastCommand 	java.util  	_robotUrl 	java.util  addToHistory 	java.util  
appendLine 	java.util  buildString 	java.util  checkConnection 	java.util  
configManager 	java.util  
dateFormat 	java.util  delay 	java.util  	emptyList 	java.util  getInstance 	java.util  
isInitialized 	java.util  launch 	java.util  launchIn 	java.util  listOf 	java.util  mapOf 	java.util  mutableStateOf 	java.util  networkManager 	java.util  onEach 	java.util  repeat 	java.util  sendCommand 	java.util  sendCommandToRobot 	java.util  to 	java.util  
toMutableList 	java.util  
getDefault java.util.Locale  ExecutorService java.util.concurrent  TimeUnit java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  SECONDS java.util.concurrent.TimeUnit  Array kotlin  CharSequence kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  Lazy kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Suppress kotlin  also kotlin  apply kotlin  getValue kotlin  
isInitialized kotlin  let kotlin  repeat kotlin  synchronized kotlin  to kotlin  forEach kotlin.Array  not kotlin.Boolean  isEmpty kotlin.CharSequence  sp 
kotlin.Double  	Companion kotlin.Enum  Icons kotlin.Enum  ImageVector kotlin.Enum  Mic kotlin.Enum  Settings kotlin.Enum  String kotlin.Enum  TouchApp kotlin.Enum  Videocam kotlin.Enum  Icons kotlin.Enum.Companion  Mic kotlin.Enum.Companion  Settings kotlin.Enum.Companion  TouchApp kotlin.Enum.Companion  Videocam kotlin.Enum.Companion  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	compareTo kotlin.Long  toString kotlin.Long  contains 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  to 
kotlin.String  toIntOrNull 
kotlin.String  toMediaType 
kotlin.String  trim 
kotlin.String  trimEnd 
kotlin.String  	trimStart 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  takeLast kotlin.collections  
toMutableList kotlin.collections  contains kotlin.collections.List  get kotlin.collections.List  
isNotEmpty kotlin.collections.List  size kotlin.collections.List  takeLast kotlin.collections.List  
toMutableList kotlin.collections.List  get kotlin.collections.Map  add kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  
startsWith 	kotlin.io  Volatile 
kotlin.jvm  contains 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  
isInitialized  kotlin.reflect.KMutableProperty0  contains kotlin.sequences  forEach kotlin.sequences  
toMutableList kotlin.sequences  
appendLine kotlin.text  buildString kotlin.text  contains kotlin.text  forEach kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  repeat kotlin.text  split kotlin.text  
startsWith kotlin.text  takeLast kotlin.text  toIntOrNull kotlin.text  
toMutableList kotlin.text  trim kotlin.text  trimEnd kotlin.text  	trimStart kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Date !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  
MultipartBody !kotlinx.coroutines.CoroutineScope  
NetworkResult !kotlinx.coroutines.CoroutineScope  	PING_PATH !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  UPLOAD_PATH !kotlinx.coroutines.CoroutineScope  
_cameraUrl !kotlinx.coroutines.CoroutineScope  _connectionStatus !kotlinx.coroutines.CoroutineScope  
_errorMessage !kotlinx.coroutines.CoroutineScope  _isConnected !kotlinx.coroutines.CoroutineScope  _isRecording !kotlinx.coroutines.CoroutineScope  _isStreaming !kotlinx.coroutines.CoroutineScope  _lastCommand !kotlinx.coroutines.CoroutineScope  _raspberryPiUrl !kotlinx.coroutines.CoroutineScope  	_robotUrl !kotlinx.coroutines.CoroutineScope  _statusMessage !kotlinx.coroutines.CoroutineScope  addToHistory !kotlinx.coroutines.CoroutineScope  
asRequestBody !kotlinx.coroutines.CoroutineScope  checkConnection !kotlinx.coroutines.CoroutineScope  checkStreamAvailability !kotlinx.coroutines.CoroutineScope  client !kotlinx.coroutines.CoroutineScope  
configManager !kotlinx.coroutines.CoroutineScope  
dateFormat !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  extractBaseUrl !kotlinx.coroutines.CoroutineScope  	formatUrl !kotlinx.coroutines.CoroutineScope  	isGranted !kotlinx.coroutines.CoroutineScope  
isInitialized !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  networkManager !kotlinx.coroutines.CoroutineScope  repeat !kotlinx.coroutines.CoroutineScope  
sendAudioFile !kotlinx.coroutines.CoroutineScope  sendCommand !kotlinx.coroutines.CoroutineScope  sendCommandToRobot !kotlinx.coroutines.CoroutineScope  startStream !kotlinx.coroutines.CoroutineScope  
stopStream !kotlinx.coroutines.CoroutineScope  toMediaType !kotlinx.coroutines.CoroutineScope  voiceRecorderManager !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  cancel kotlinx.coroutines.Job  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  onEach !kotlinx.coroutines.flow.StateFlow  Boolean okhttp3  Cache okhttp3  Call okhttp3  ConnectionPool okhttp3  
Dispatcher okhttp3  Dispatchers okhttp3  	Exception okhttp3  File okhttp3  HttpUrl okhttp3  IOException okhttp3  Int okhttp3  Interceptor okhttp3  Log okhttp3  	MediaType okhttp3  
MultipartBody okhttp3  
NetworkResult okhttp3  OkHttpClient okhttp3  	PING_PATH okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  ResponseBody okhttp3  String okhttp3  System okhttp3  TAG okhttp3  TimeUnit okhttp3  UPLOAD_PATH okhttp3  Unit okhttp3  
asRequestBody okhttp3  client okhttp3  	formatUrl okhttp3  java okhttp3  
startsWith okhttp3  toMediaType okhttp3  trimEnd okhttp3  	trimStart okhttp3  withContext okhttp3  close 
okhttp3.Cache  execute okhttp3.Call  evictAll okhttp3.ConnectionPool  executorService okhttp3.Dispatcher  <SAM-CONSTRUCTOR> okhttp3.Interceptor  Chain okhttp3.Interceptor  proceed okhttp3.Interceptor.Chain  request okhttp3.Interceptor.Chain  toMediaType okhttp3.MediaType.Companion  Builder okhttp3.MultipartBody  	Companion okhttp3.MultipartBody  FORM okhttp3.MultipartBody  addFormDataPart okhttp3.MultipartBody.Builder  build okhttp3.MultipartBody.Builder  setType okhttp3.MultipartBody.Builder  FORM okhttp3.MultipartBody.Companion  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  cache okhttp3.OkHttpClient  connectionPool okhttp3.OkHttpClient  
dispatcher okhttp3.OkHttpClient  newCall okhttp3.OkHttpClient  -addInterceptor okhttp3.OkHttpClient.Builder  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  url okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  get okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  
asRequestBody okhttp3.RequestBody.Companion  body okhttp3.Response  close okhttp3.Response  code okhttp3.Response  isSuccessful okhttp3.Response  string okhttp3.ResponseBody  net okhttp3.java  ConnectException okhttp3.java.net  SocketTimeoutException okhttp3.java.net  UnknownHostException okhttp3.java.net  DebugLogManager android.app.Activity  LogLevel android.app.Activity  addLog android.app.Activity  
getSystemInfo android.app.Activity  DebugLogManager android.content.Context  LogLevel android.content.Context  addLog android.content.Context  
getSystemInfo android.content.Context  DebugLogManager android.content.ContextWrapper  LogLevel android.content.ContextWrapper  addLog android.content.ContextWrapper  
getSystemInfo android.content.ContextWrapper  BRAND android.os.Build  MODEL android.os.Build  RELEASE android.os.Build.VERSION  getStackTraceString android.util.Log  i android.util.Log  DebugLogManager  android.view.ContextThemeWrapper  LogLevel  android.view.ContextThemeWrapper  addLog  android.view.ContextThemeWrapper  
getSystemInfo  android.view.ContextThemeWrapper  DebugLogManager #androidx.activity.ComponentActivity  LogLevel #androidx.activity.ComponentActivity  addLog #androidx.activity.ComponentActivity  
getSystemInfo #androidx.activity.ComponentActivity  DebugLogScreen /androidx.compose.animation.AnimatedContentScope  Screen /androidx.compose.animation.AnimatedContentScope  AnnotatedString "androidx.compose.foundation.layout  	BugReport "androidx.compose.foundation.layout  
DebugLogEntry "androidx.compose.foundation.layout  DebugLogManager "androidx.compose.foundation.layout  DebugLogScreen "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LogEntryCard "androidx.compose.foundation.layout  LogLevel "androidx.compose.foundation.layout  LogLevelCounter "androidx.compose.foundation.layout  SelectionContainer "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  addLog "androidx.compose.foundation.layout  	clearLogs "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  count "androidx.compose.foundation.layout  exportLogsAsText "androidx.compose.foundation.layout  
getSystemInfo "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  	BugReport .androidx.compose.foundation.layout.ColumnScope  DebugLogManager .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LogEntryCard .androidx.compose.foundation.layout.ColumnScope  LogLevel .androidx.compose.foundation.layout.ColumnScope  LogLevelCounter .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  SelectionContainer .androidx.compose.foundation.layout.ColumnScope  Surface .androidx.compose.foundation.layout.ColumnScope  count .androidx.compose.foundation.layout.ColumnScope  
getSystemInfo .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  AnnotatedString +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	BugReport +androidx.compose.foundation.layout.RowScope  Clear +androidx.compose.foundation.layout.RowScope  DebugLogManager +androidx.compose.foundation.layout.RowScope  LogLevel +androidx.compose.foundation.layout.RowScope  LogLevelCounter +androidx.compose.foundation.layout.RowScope  Search +androidx.compose.foundation.layout.RowScope  Share +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  	clearLogs +androidx.compose.foundation.layout.RowScope  count +androidx.compose.foundation.layout.RowScope  exportLogsAsText +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  LogEntryCard .androidx.compose.foundation.lazy.LazyItemScope  LogEntryCard .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  SelectionContainer *androidx.compose.foundation.text.selection  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  	BugReport ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Share ,androidx.compose.material.icons.Icons.Filled  	ArrowBack &androidx.compose.material.icons.filled  	BugReport &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  DebugLogManager &androidx.compose.material.icons.filled  DebugLogScreen &androidx.compose.material.icons.filled  LogLevel &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  addLog &androidx.compose.material.icons.filled  
getSystemInfo &androidx.compose.material.icons.filled  AnnotatedString androidx.compose.material3  	BugReport androidx.compose.material3  
DebugLogEntry androidx.compose.material3  DebugLogManager androidx.compose.material3  DebugLogScreen androidx.compose.material3  
LazyColumn androidx.compose.material3  LogEntryCard androidx.compose.material3  LogLevel androidx.compose.material3  LogLevelCounter androidx.compose.material3  SelectionContainer androidx.compose.material3  
TextButton androidx.compose.material3  addLog androidx.compose.material3  	clearLogs androidx.compose.material3  collectAsState androidx.compose.material3  count androidx.compose.material3  exportLogsAsText androidx.compose.material3  
getSystemInfo androidx.compose.material3  let androidx.compose.material3  	onPrimary &androidx.compose.material3.ColorScheme  labelMedium %androidx.compose.material3.Typography  AnnotatedString androidx.compose.runtime  	BugReport androidx.compose.runtime  
DebugLogEntry androidx.compose.runtime  DebugLogManager androidx.compose.runtime  DebugLogScreen androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LogEntryCard androidx.compose.runtime  LogLevel androidx.compose.runtime  LogLevelCounter androidx.compose.runtime  SelectionContainer androidx.compose.runtime  
TextButton androidx.compose.runtime  addLog androidx.compose.runtime  	clearLogs androidx.compose.runtime  collectAsState androidx.compose.runtime  count androidx.compose.runtime  exportLogsAsText androidx.compose.runtime  
getSystemInfo androidx.compose.runtime  let androidx.compose.runtime  
background &androidx.compose.ui.Modifier.Companion  Blue "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  ClipboardManager androidx.compose.ui.platform  LocalClipboardManager androidx.compose.ui.platform  setText -androidx.compose.ui.platform.ClipboardManager  AnnotatedString androidx.compose.ui.text  DebugLogManager #androidx.core.app.ComponentActivity  LogLevel #androidx.core.app.ComponentActivity  addLog #androidx.core.app.ComponentActivity  
getSystemInfo #androidx.core.app.ComponentActivity  
navigateUp !androidx.navigation.NavController  DebugLogScreen #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  
navigateUp %androidx.navigation.NavHostController  AnnotatedString com.example.voicecontrolapp  	BugReport com.example.voicecontrolapp  
DebugLogEntry com.example.voicecontrolapp  DebugLogManager com.example.voicecontrolapp  DebugLogScreen com.example.voicecontrolapp  
LazyColumn com.example.voicecontrolapp  LogEntryCard com.example.voicecontrolapp  LogLevel com.example.voicecontrolapp  LogLevelCounter com.example.voicecontrolapp  Long com.example.voicecontrolapp  SelectionContainer com.example.voicecontrolapp  
StringBuilder com.example.voicecontrolapp  
TextButton com.example.voicecontrolapp  	Throwable com.example.voicecontrolapp  addConnectionTestLog com.example.voicecontrolapp  addLog com.example.voicecontrolapp  
addNetworkLog com.example.voicecontrolapp  android com.example.voicecontrolapp  	clearLogs com.example.voicecontrolapp  collectAsState com.example.voicecontrolapp  count com.example.voicecontrolapp  exportLogsAsText com.example.voicecontrolapp  
getSystemInfo com.example.voicecontrolapp  	javaClass com.example.voicecontrolapp  take com.example.voicecontrolapp  
getConfigInfo )com.example.voicecontrolapp.ConfigManager  level )com.example.voicecontrolapp.DebugLogEntry  message )com.example.voicecontrolapp.DebugLogEntry  tag )com.example.voicecontrolapp.DebugLogEntry  	throwable )com.example.voicecontrolapp.DebugLogEntry  
timeString )com.example.voicecontrolapp.DebugLogEntry  Date +com.example.voicecontrolapp.DebugLogManager  
DebugLogEntry +com.example.voicecontrolapp.DebugLogManager  Locale +com.example.voicecontrolapp.DebugLogManager  Log +com.example.voicecontrolapp.DebugLogManager  LogLevel +com.example.voicecontrolapp.DebugLogManager  MAX_LOGS +com.example.voicecontrolapp.DebugLogManager  MutableStateFlow +com.example.voicecontrolapp.DebugLogManager  SimpleDateFormat +com.example.voicecontrolapp.DebugLogManager  System +com.example.voicecontrolapp.DebugLogManager  TAG +com.example.voicecontrolapp.DebugLogManager  _logs +com.example.voicecontrolapp.DebugLogManager  addConnectionTestLog +com.example.voicecontrolapp.DebugLogManager  addLog +com.example.voicecontrolapp.DebugLogManager  
addNetworkLog +com.example.voicecontrolapp.DebugLogManager  android +com.example.voicecontrolapp.DebugLogManager  
appendLine +com.example.voicecontrolapp.DebugLogManager  asStateFlow +com.example.voicecontrolapp.DebugLogManager  buildString +com.example.voicecontrolapp.DebugLogManager  	clearLogs +com.example.voicecontrolapp.DebugLogManager  
dateFormat +com.example.voicecontrolapp.DebugLogManager  	emptyList +com.example.voicecontrolapp.DebugLogManager  exportLogsAsText +com.example.voicecontrolapp.DebugLogManager  
getSystemInfo +com.example.voicecontrolapp.DebugLogManager  let +com.example.voicecontrolapp.DebugLogManager  logs +com.example.voicecontrolapp.DebugLogManager  
toMutableList +com.example.voicecontrolapp.DebugLogManager  DEBUG $com.example.voicecontrolapp.LogLevel  ERROR $com.example.voicecontrolapp.LogLevel  INFO $com.example.voicecontrolapp.LogLevel  WARN $com.example.voicecontrolapp.LogLevel  androidx $com.example.voicecontrolapp.LogLevel  color $com.example.voicecontrolapp.LogLevel  displayName $com.example.voicecontrolapp.LogLevel  name $com.example.voicecontrolapp.LogLevel  DebugLogManager (com.example.voicecontrolapp.MainActivity  LogLevel (com.example.voicecontrolapp.MainActivity  addLog (com.example.voicecontrolapp.MainActivity  
getSystemInfo (com.example.voicecontrolapp.MainActivity  DebugLogManager )com.example.voicecontrolapp.MainViewModel  LogLevel )com.example.voicecontrolapp.MainViewModel  addLog )com.example.voicecontrolapp.MainViewModel  performNetworkDiagnosis )com.example.voicecontrolapp.MainViewModel  DebugLogManager 3com.example.voicecontrolapp.MainViewModel.Companion  LogLevel 3com.example.voicecontrolapp.MainViewModel.Companion  addLog 3com.example.voicecontrolapp.MainViewModel.Companion  DebugLogManager *com.example.voicecontrolapp.NetworkManager  LogLevel *com.example.voicecontrolapp.NetworkManager  
StringBuilder *com.example.voicecontrolapp.NetworkManager  addConnectionTestLog *com.example.voicecontrolapp.NetworkManager  addLog *com.example.voicecontrolapp.NetworkManager  
addNetworkLog *com.example.voicecontrolapp.NetworkManager  
appendLine *com.example.voicecontrolapp.NetworkManager  	javaClass *com.example.voicecontrolapp.NetworkManager  networkDiagnosis *com.example.voicecontrolapp.NetworkManager  take *com.example.voicecontrolapp.NetworkManager  DebugLogManager 4com.example.voicecontrolapp.NetworkManager.Companion  LogLevel 4com.example.voicecontrolapp.NetworkManager.Companion  
StringBuilder 4com.example.voicecontrolapp.NetworkManager.Companion  addConnectionTestLog 4com.example.voicecontrolapp.NetworkManager.Companion  addLog 4com.example.voicecontrolapp.NetworkManager.Companion  
addNetworkLog 4com.example.voicecontrolapp.NetworkManager.Companion  
appendLine 4com.example.voicecontrolapp.NetworkManager.Companion  java 4com.example.voicecontrolapp.NetworkManager.Companion  	javaClass 4com.example.voicecontrolapp.NetworkManager.Companion  take 4com.example.voicecontrolapp.NetworkManager.Companion  	BugReport "com.example.voicecontrolapp.Screen  DebugLog "com.example.voicecontrolapp.Screen  compose $com.example.voicecontrolapp.androidx  ui ,com.example.voicecontrolapp.androidx.compose  graphics /com.example.voicecontrolapp.androidx.compose.ui  Color 8com.example.voicecontrolapp.androidx.compose.ui.graphics  Class 	java.lang  
simpleName java.lang.Class  	javaClass java.lang.Exception  Date java.lang.StringBuilder  Locale java.lang.StringBuilder  Log java.lang.StringBuilder  SimpleDateFormat java.lang.StringBuilder  android java.lang.StringBuilder  append java.lang.StringBuilder  let java.lang.StringBuilder  toString java.lang.StringBuilder  URI java.net  message java.net.ConnectException  message java.net.SocketTimeoutException  host java.net.URI  port java.net.URI  scheme java.net.URI  message java.net.UnknownHostException  
DebugLogEntry 	java.util  Int 	java.util  LogLevel 	java.util  Long 	java.util  MutableStateFlow 	java.util  	StateFlow 	java.util  	Throwable 	java.util  android 	java.util  androidx 	java.util  asStateFlow 	java.util  forEach 	java.util  let 	java.util  compose java.util.androidx  ui java.util.androidx.compose  graphics java.util.androidx.compose.ui  Color &java.util.androidx.compose.ui.graphics  	Throwable kotlin  	BugReport kotlin.Enum  androidx kotlin.Enum  	BugReport kotlin.Enum.Companion  androidx kotlin.Enum.Companion  compose kotlin.Enum.androidx  ui kotlin.Enum.androidx.compose  graphics kotlin.Enum.androidx.compose.ui  Color (kotlin.Enum.androidx.compose.ui.graphics  let 
kotlin.Int  minus 
kotlin.Int  toString 
kotlin.Int  minus kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  length 
kotlin.String  take 
kotlin.String  let kotlin.Throwable  count kotlin.collections  take kotlin.collections  count kotlin.collections.List  isEmpty kotlin.collections.List  java 
kotlin.jvm  	javaClass 
kotlin.jvm  KClass kotlin.reflect  Sequence kotlin.sequences  count kotlin.sequences  take kotlin.sequences  count kotlin.text  take kotlin.text  DebugLogManager !kotlinx.coroutines.CoroutineScope  LogLevel !kotlinx.coroutines.CoroutineScope  
StringBuilder !kotlinx.coroutines.CoroutineScope  addConnectionTestLog !kotlinx.coroutines.CoroutineScope  addLog !kotlinx.coroutines.CoroutineScope  
addNetworkLog !kotlinx.coroutines.CoroutineScope  
appendLine !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  	javaClass !kotlinx.coroutines.CoroutineScope  take !kotlinx.coroutines.CoroutineScope  collectAsState !kotlinx.coroutines.flow.StateFlow  DebugLogManager okhttp3  Headers okhttp3  LogLevel okhttp3  Protocol okhttp3  
StringBuilder okhttp3  addConnectionTestLog okhttp3  addLog okhttp3  
addNetworkLog okhttp3  
appendLine okhttp3  forEach okhttp3  	javaClass okhttp3  take okhttp3  headers okhttp3.Response  protocol okhttp3.Response  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  apply android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  
WebViewClient android.webkit.WebView  apply android.webkit.WebView  loadDataWithBaseURL android.webkit.WebView  settings android.webkit.WebView  
trimIndent android.webkit.WebView  
webViewClient android.webkit.WebView  AndroidView "androidx.compose.foundation.layout  WebView "androidx.compose.foundation.layout  
WebViewClient "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
trimIndent "androidx.compose.foundation.layout  AndroidView +androidx.compose.foundation.layout.BoxScope  WebView +androidx.compose.foundation.layout.BoxScope  
WebViewClient +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  
trimIndent +androidx.compose.foundation.layout.BoxScope  AndroidView .androidx.compose.foundation.layout.ColumnScope  WebView .androidx.compose.foundation.layout.ColumnScope  
WebViewClient .androidx.compose.foundation.layout.ColumnScope  apply .androidx.compose.foundation.layout.ColumnScope  
trimIndent .androidx.compose.foundation.layout.ColumnScope  AndroidView androidx.compose.material3  WebView androidx.compose.material3  
WebViewClient androidx.compose.material3  apply androidx.compose.material3  
trimIndent androidx.compose.material3  AndroidView androidx.compose.runtime  WebView androidx.compose.runtime  
WebViewClient androidx.compose.runtime  apply androidx.compose.runtime  
trimIndent androidx.compose.runtime  AndroidView androidx.compose.ui.viewinterop  AndroidView com.example.voicecontrolapp  WebView com.example.voicecontrolapp  
WebViewClient com.example.voicecontrolapp  
trimIndent com.example.voicecontrolapp  DebugLogManager +com.example.voicecontrolapp.CameraViewModel  LogLevel +com.example.voicecontrolapp.CameraViewModel  NetworkManager +com.example.voicecontrolapp.CameraViewModel  addLog +com.example.voicecontrolapp.CameraViewModel  networkManager +com.example.voicecontrolapp.CameraViewModel  DebugLogManager 5com.example.voicecontrolapp.CameraViewModel.Companion  LogLevel 5com.example.voicecontrolapp.CameraViewModel.Companion  NetworkManager 5com.example.voicecontrolapp.CameraViewModel.Companion  addLog 5com.example.voicecontrolapp.CameraViewModel.Companion  networkManager 5com.example.voicecontrolapp.CameraViewModel.Companion  
trimIndent 
kotlin.String  
trimIndent kotlin.text  BLACK android.graphics.Color  
ColorDrawable android.graphics.drawable  Drawable android.graphics.drawable  
background android.view.View  	ImageView android.widget  	ImageView android.widget.ImageView  	ScaleType android.widget.ImageView  android android.widget.ImageView  apply android.widget.ImageView  
background android.widget.ImageView  let android.widget.ImageView  	scaleType android.widget.ImageView  
FIT_CENTER "android.widget.ImageView.ScaleType  	Exception "androidx.compose.foundation.layout  Glide "androidx.compose.foundation.layout  	ImageView "androidx.compose.foundation.layout  MjpegStreamDisplay "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Glide +androidx.compose.foundation.layout.BoxScope  	ImageView +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  MjpegStreamDisplay +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  com +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  MjpegStreamDisplay .androidx.compose.foundation.layout.ColumnScope  	Exception androidx.compose.material3  Glide androidx.compose.material3  	ImageView androidx.compose.material3  MjpegStreamDisplay androidx.compose.material3  android androidx.compose.material3  com androidx.compose.material3  	Exception androidx.compose.runtime  Glide androidx.compose.runtime  	ImageView androidx.compose.runtime  MjpegStreamDisplay androidx.compose.runtime  android androidx.compose.runtime  com androidx.compose.runtime  Glide com.bumptech.glide  RequestBuilder com.bumptech.glide  RequestManager com.bumptech.glide  with com.bumptech.glide.Glide  diskCacheStrategy !com.bumptech.glide.RequestBuilder  into !com.bumptech.glide.RequestBuilder  load !com.bumptech.glide.RequestBuilder  skipMemoryCache !com.bumptech.glide.RequestBuilder  asGif !com.bumptech.glide.RequestManager  
DataSource com.bumptech.glide.load  GlideException com.bumptech.glide.load.engine  NONE 0com.bumptech.glide.load.engine.DiskCacheStrategy  RequestListener com.bumptech.glide.request  Target !com.bumptech.glide.request.target  
ViewTarget !com.bumptech.glide.request.target  Glide com.example.voicecontrolapp  	ImageView com.example.voicecontrolapp  MjpegStreamDisplay com.example.voicecontrolapp  com com.example.voicecontrolapp  black android.R.color  transparent android.R.color  error !com.bumptech.glide.RequestBuilder  placeholder !com.bumptech.glide.RequestBuilder  load !com.bumptech.glide.RequestManager  
holo_red_dark android.R.color  DiskCacheStrategy "androidx.compose.foundation.layout  DiskCacheStrategy +androidx.compose.foundation.layout.BoxScope  DiskCacheStrategy androidx.compose.material3  DiskCacheStrategy androidx.compose.runtime  timeout !com.bumptech.glide.RequestBuilder  DiskCacheStrategy com.bumptech.glide.load.engine  DiskCacheStrategy com.example.voicecontrolapp  setBackgroundColor android.view.View  Log android.webkit.WebView  android android.webkit.WebView  loadData android.webkit.WebView  setBackgroundColor android.webkit.WebView  Log android.webkit.WebViewClient  setBackgroundColor android.widget.ImageView  OutlinedButton +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  ic_menu_close_clear_cancel android.R.drawable  ic_menu_gallery android.R.drawable  Bitmap android.graphics  Handler 
android.os  postDelayed android.os.Handler  
getMainLooper android.os.Looper  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  android android.webkit.WebViewClient  graphics *androidx.compose.foundation.layout.android  Bitmap 3androidx.compose.foundation.layout.android.graphics  graphics "androidx.compose.material3.android  Bitmap +androidx.compose.material3.android.graphics  graphics  androidx.compose.runtime.android  Bitmap )androidx.compose.runtime.android.graphics  graphics #com.example.voicecontrolapp.android  Bitmap ,com.example.voicecontrolapp.android.graphics  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  Arrangement +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  Log .androidx.compose.foundation.layout.ColumnScope  Log +androidx.compose.foundation.layout.RowScope  DisposableEffect "androidx.compose.foundation.layout  	ExoPlayer "androidx.compose.foundation.layout  	MediaItem "androidx.compose.foundation.layout  Player "androidx.compose.foundation.layout  
PlayerView "androidx.compose.foundation.layout  	ExoPlayer +androidx.compose.foundation.layout.BoxScope  	MediaItem +androidx.compose.foundation.layout.BoxScope  Player +androidx.compose.foundation.layout.BoxScope  
PlayerView +androidx.compose.foundation.layout.BoxScope  Listener )androidx.compose.foundation.layout.Player  media3 +androidx.compose.foundation.layout.androidx  common 2androidx.compose.foundation.layout.androidx.media3  PlaybackException 9androidx.compose.foundation.layout.androidx.media3.common  DisposableEffect androidx.compose.material3  	ExoPlayer androidx.compose.material3  	MediaItem androidx.compose.material3  Player androidx.compose.material3  
PlayerView androidx.compose.material3  Listener !androidx.compose.material3.Player  media3 #androidx.compose.material3.androidx  common *androidx.compose.material3.androidx.media3  PlaybackException 1androidx.compose.material3.androidx.media3.common  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  	ExoPlayer androidx.compose.runtime  	MediaItem androidx.compose.runtime  Player androidx.compose.runtime  
PlayerView androidx.compose.runtime  	onDispose .androidx.compose.runtime.DisposableEffectScope  Listener androidx.compose.runtime.Player  media3 !androidx.compose.runtime.androidx  common (androidx.compose.runtime.androidx.media3  PlaybackException /androidx.compose.runtime.androidx.media3.common  	MediaItem androidx.media3.common  PlaybackException androidx.media3.common  Player androidx.media3.common  fromUri  androidx.media3.common.MediaItem  message (androidx.media3.common.PlaybackException  Listener androidx.media3.common.Player  STATE_BUFFERING androidx.media3.common.Player  STATE_ENDED androidx.media3.common.Player  
STATE_IDLE androidx.media3.common.Player  STATE_READY androidx.media3.common.Player  addListener androidx.media3.common.Player  play androidx.media3.common.Player  prepare androidx.media3.common.Player  release androidx.media3.common.Player  setMediaItem androidx.media3.common.Player  	ExoPlayer androidx.media3.exoplayer  Builder #androidx.media3.exoplayer.ExoPlayer  addListener #androidx.media3.exoplayer.ExoPlayer  let #androidx.media3.exoplayer.ExoPlayer  play #androidx.media3.exoplayer.ExoPlayer  prepare #androidx.media3.exoplayer.ExoPlayer  release #androidx.media3.exoplayer.ExoPlayer  setMediaItem #androidx.media3.exoplayer.ExoPlayer  build +androidx.media3.exoplayer.ExoPlayer.Builder  
PlayerView androidx.media3.ui  	ExoPlayer androidx.media3.ui.PlayerView  Log androidx.media3.ui.PlayerView  	MediaItem androidx.media3.ui.PlayerView  Player androidx.media3.ui.PlayerView  android androidx.media3.ui.PlayerView  apply androidx.media3.ui.PlayerView  player androidx.media3.ui.PlayerView  setBackgroundColor androidx.media3.ui.PlayerView  
useController androidx.media3.ui.PlayerView  DisposableEffect com.example.voicecontrolapp  	ExoPlayer com.example.voicecontrolapp  	MediaItem com.example.voicecontrolapp  Player com.example.voicecontrolapp  
PlayerView com.example.voicecontrolapp  Listener "com.example.voicecontrolapp.Player  media3 $com.example.voicecontrolapp.androidx  common +com.example.voicecontrolapp.androidx.media3  PlaybackException 2com.example.voicecontrolapp.androidx.media3.common  WebResourceRequest android.webkit  WebResourceResponse android.webkit  
statusCode "android.webkit.WebResourceResponse  
LOAD_NO_CACHE android.webkit.WebSettings  android android.webkit.WebSettings  	cacheMode android.webkit.WebSettings   mediaPlaybackRequiresUserGesture android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  Refresh +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  webkit *androidx.compose.foundation.layout.android  WebResourceRequest 1androidx.compose.foundation.layout.android.webkit  WebResourceResponse 1androidx.compose.foundation.layout.android.webkit  webkit "androidx.compose.material3.android  WebResourceRequest )androidx.compose.material3.android.webkit  WebResourceResponse )androidx.compose.material3.android.webkit  webkit  androidx.compose.runtime.android  WebResourceRequest 'androidx.compose.runtime.android.webkit  WebResourceResponse 'androidx.compose.runtime.android.webkit  handleStreamError com.example.voicecontrolapp  
retryCount com.example.voicecontrolapp  retryDelayMs com.example.voicecontrolapp  startStreamWithRetry com.example.voicecontrolapp  handleStreamError +com.example.voicecontrolapp.CameraViewModel  
maxRetries +com.example.voicecontrolapp.CameraViewModel  
retryCount +com.example.voicecontrolapp.CameraViewModel  retryDelayMs +com.example.voicecontrolapp.CameraViewModel  retryJob +com.example.voicecontrolapp.CameraViewModel  startStreamWithRetry +com.example.voicecontrolapp.CameraViewModel  handleStreamError 5com.example.voicecontrolapp.CameraViewModel.Companion  
retryCount 5com.example.voicecontrolapp.CameraViewModel.Companion  retryDelayMs 5com.example.voicecontrolapp.CameraViewModel.Companion  startStreamWithRetry 5com.example.voicecontrolapp.CameraViewModel.Companion  checkMjpegStreamAvailability *com.example.voicecontrolapp.NetworkManager  contains *com.example.voicecontrolapp.NetworkManager  contains 4com.example.voicecontrolapp.NetworkManager.Companion  webkit #com.example.voicecontrolapp.android  WebResourceRequest *com.example.voicecontrolapp.android.webkit  WebResourceResponse *com.example.voicecontrolapp.android.webkit  inc 
kotlin.Int  div kotlin.Long  TimeUnit !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  handleStreamError !kotlinx.coroutines.CoroutineScope  
retryCount !kotlinx.coroutines.CoroutineScope  retryDelayMs !kotlinx.coroutines.CoroutineScope  startStreamWithRetry !kotlinx.coroutines.CoroutineScope  contains okhttp3  
newBuilder okhttp3.OkHttpClient  head okhttp3.Request.Builder  header okhttp3.Response  ic_dialog_alert android.R.drawable  
BitmapFactory android.graphics  decodeByteArray android.graphics.BitmapFactory  AttributeSet android.util  BOUNDARY_MARKER android.view.View  
BitmapFactory android.view.View  	ByteArray android.view.View  ByteArrayOutputStream android.view.View  CONTENT_TYPE_MARKER android.view.View  Charsets android.view.View  CoroutineScope android.view.View  Dispatchers android.view.View  	Exception android.view.View  IOException android.view.View  Log android.view.View  OkHttpClient android.view.View  Request android.view.View  	ScaleType android.view.View  String android.view.View  TAG android.view.View  TimeUnit android.view.View  android android.view.View  contains android.view.View  copyOfRange android.view.View  indexOf android.view.View  invoke android.view.View  launch android.view.View  playMjpegStream android.view.View  setImageBitmap android.view.View  setImageResource android.view.View  withContext android.view.View  BOUNDARY_MARKER android.widget.ImageView  
BitmapFactory android.widget.ImageView  	ByteArray android.widget.ImageView  ByteArrayOutputStream android.widget.ImageView  CONTENT_TYPE_MARKER android.widget.ImageView  Charsets android.widget.ImageView  CoroutineScope android.widget.ImageView  Dispatchers android.widget.ImageView  	Exception android.widget.ImageView  IOException android.widget.ImageView  Log android.widget.ImageView  OkHttpClient android.widget.ImageView  Request android.widget.ImageView  String android.widget.ImageView  TAG android.widget.ImageView  TimeUnit android.widget.ImageView  contains android.widget.ImageView  copyOfRange android.widget.ImageView  indexOf android.widget.ImageView  invoke android.widget.ImageView  launch android.widget.ImageView  onDetachedFromWindow android.widget.ImageView  playMjpegStream android.widget.ImageView  setImageBitmap android.widget.ImageView  setImageResource android.widget.ImageView  withContext android.widget.ImageView  MjpegStreamView "androidx.compose.foundation.layout  MjpegStreamView +androidx.compose.foundation.layout.BoxScope  MjpegStreamView androidx.compose.material3  MjpegStreamView androidx.compose.runtime  AttributeSet com.example.voicecontrolapp  BOUNDARY_MARKER com.example.voicecontrolapp  
BitmapFactory com.example.voicecontrolapp  	ByteArray com.example.voicecontrolapp  ByteArrayOutputStream com.example.voicecontrolapp  CONTENT_TYPE_MARKER com.example.voicecontrolapp  Charsets com.example.voicecontrolapp  CoroutineScope com.example.voicecontrolapp  InputStream com.example.voicecontrolapp  JvmOverloads com.example.voicecontrolapp  MjpegStreamView com.example.voicecontrolapp  	ScaleType com.example.voicecontrolapp  copyOfRange com.example.voicecontrolapp  indexOf com.example.voicecontrolapp  invoke com.example.voicecontrolapp  playMjpegStream com.example.voicecontrolapp  setImageBitmap com.example.voicecontrolapp  setImageResource com.example.voicecontrolapp  AttributeSet +com.example.voicecontrolapp.MjpegStreamView  BOUNDARY_MARKER +com.example.voicecontrolapp.MjpegStreamView  
BitmapFactory +com.example.voicecontrolapp.MjpegStreamView  	ByteArray +com.example.voicecontrolapp.MjpegStreamView  ByteArrayOutputStream +com.example.voicecontrolapp.MjpegStreamView  CONTENT_TYPE_MARKER +com.example.voicecontrolapp.MjpegStreamView  Charsets +com.example.voicecontrolapp.MjpegStreamView  Context +com.example.voicecontrolapp.MjpegStreamView  CoroutineScope +com.example.voicecontrolapp.MjpegStreamView  Dispatchers +com.example.voicecontrolapp.MjpegStreamView  	Exception +com.example.voicecontrolapp.MjpegStreamView  IOException +com.example.voicecontrolapp.MjpegStreamView  InputStream +com.example.voicecontrolapp.MjpegStreamView  Int +com.example.voicecontrolapp.MjpegStreamView  Job +com.example.voicecontrolapp.MjpegStreamView  JvmOverloads +com.example.voicecontrolapp.MjpegStreamView  Log +com.example.voicecontrolapp.MjpegStreamView  OkHttpClient +com.example.voicecontrolapp.MjpegStreamView  Request +com.example.voicecontrolapp.MjpegStreamView  	ScaleType +com.example.voicecontrolapp.MjpegStreamView  String +com.example.voicecontrolapp.MjpegStreamView  TAG +com.example.voicecontrolapp.MjpegStreamView  TimeUnit +com.example.voicecontrolapp.MjpegStreamView  android +com.example.voicecontrolapp.MjpegStreamView  apply +com.example.voicecontrolapp.MjpegStreamView  client +com.example.voicecontrolapp.MjpegStreamView  contains +com.example.voicecontrolapp.MjpegStreamView  copyOfRange +com.example.voicecontrolapp.MjpegStreamView  indexOf +com.example.voicecontrolapp.MjpegStreamView  invoke +com.example.voicecontrolapp.MjpegStreamView  isStreaming +com.example.voicecontrolapp.MjpegStreamView  launch +com.example.voicecontrolapp.MjpegStreamView  parseMjpegStream +com.example.voicecontrolapp.MjpegStreamView  playMjpegStream +com.example.voicecontrolapp.MjpegStreamView  processFrame +com.example.voicecontrolapp.MjpegStreamView  	scaleType +com.example.voicecontrolapp.MjpegStreamView  setBackgroundColor +com.example.voicecontrolapp.MjpegStreamView  setImageBitmap +com.example.voicecontrolapp.MjpegStreamView  setImageResource +com.example.voicecontrolapp.MjpegStreamView  startStream +com.example.voicecontrolapp.MjpegStreamView  
stopStream +com.example.voicecontrolapp.MjpegStreamView  	streamJob +com.example.voicecontrolapp.MjpegStreamView  	streamUrl +com.example.voicecontrolapp.MjpegStreamView  withContext +com.example.voicecontrolapp.MjpegStreamView  BOUNDARY_MARKER 5com.example.voicecontrolapp.MjpegStreamView.Companion  
BitmapFactory 5com.example.voicecontrolapp.MjpegStreamView.Companion  	ByteArray 5com.example.voicecontrolapp.MjpegStreamView.Companion  ByteArrayOutputStream 5com.example.voicecontrolapp.MjpegStreamView.Companion  CONTENT_TYPE_MARKER 5com.example.voicecontrolapp.MjpegStreamView.Companion  Charsets 5com.example.voicecontrolapp.MjpegStreamView.Companion  CoroutineScope 5com.example.voicecontrolapp.MjpegStreamView.Companion  Dispatchers 5com.example.voicecontrolapp.MjpegStreamView.Companion  IOException 5com.example.voicecontrolapp.MjpegStreamView.Companion  Log 5com.example.voicecontrolapp.MjpegStreamView.Companion  OkHttpClient 5com.example.voicecontrolapp.MjpegStreamView.Companion  Request 5com.example.voicecontrolapp.MjpegStreamView.Companion  	ScaleType 5com.example.voicecontrolapp.MjpegStreamView.Companion  String 5com.example.voicecontrolapp.MjpegStreamView.Companion  TAG 5com.example.voicecontrolapp.MjpegStreamView.Companion  TimeUnit 5com.example.voicecontrolapp.MjpegStreamView.Companion  android 5com.example.voicecontrolapp.MjpegStreamView.Companion  contains 5com.example.voicecontrolapp.MjpegStreamView.Companion  copyOfRange 5com.example.voicecontrolapp.MjpegStreamView.Companion  indexOf 5com.example.voicecontrolapp.MjpegStreamView.Companion  invoke 5com.example.voicecontrolapp.MjpegStreamView.Companion  launch 5com.example.voicecontrolapp.MjpegStreamView.Companion  playMjpegStream 5com.example.voicecontrolapp.MjpegStreamView.Companion  setImageBitmap 5com.example.voicecontrolapp.MjpegStreamView.Companion  setImageResource 5com.example.voicecontrolapp.MjpegStreamView.Companion  withContext 5com.example.voicecontrolapp.MjpegStreamView.Companion  ByteArrayOutputStream java.io  InputStream java.io  reset java.io.ByteArrayOutputStream  size java.io.ByteArrayOutputStream  toByteArray java.io.ByteArrayOutputStream  write java.io.ByteArrayOutputStream  close java.io.InputStream  read java.io.InputStream  write java.io.OutputStream  Charset java.nio.charset  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  
FloatArray kotlin  IntArray kotlin  	LongArray kotlin  
ShortArray kotlin  String kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  copyOfRange kotlin.ByteArray  size kotlin.ByteArray  plus 
kotlin.Int  rem 
kotlin.Int  	Companion 
kotlin.String  indexOf 
kotlin.String  invoke 
kotlin.String  invoke kotlin.String.Companion  copyOfRange kotlin.collections  copyOfRangeInline kotlin.collections  indexOf kotlin.collections  JvmOverloads 
kotlin.jvm  indexOf kotlin.sequences  Charsets kotlin.text  String kotlin.text  indexOf kotlin.text  
ISO_8859_1 kotlin.text.Charsets  AttributeSet kotlinx.coroutines  BOUNDARY_MARKER kotlinx.coroutines  
BitmapFactory kotlinx.coroutines  	ByteArray kotlinx.coroutines  ByteArrayOutputStream kotlinx.coroutines  CONTENT_TYPE_MARKER kotlinx.coroutines  Charsets kotlinx.coroutines  Context kotlinx.coroutines  	Exception kotlinx.coroutines  IOException kotlinx.coroutines  	ImageView kotlinx.coroutines  InputStream kotlinx.coroutines  Int kotlinx.coroutines  JvmOverloads kotlinx.coroutines  Log kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  OkHttpClient kotlinx.coroutines  Request kotlinx.coroutines  	ScaleType kotlinx.coroutines  String kotlinx.coroutines  TAG kotlinx.coroutines  TimeUnit kotlinx.coroutines  android kotlinx.coroutines  contains kotlinx.coroutines  copyOfRange kotlinx.coroutines  indexOf kotlinx.coroutines  invoke kotlinx.coroutines  playMjpegStream kotlinx.coroutines  setImageBitmap kotlinx.coroutines  setImageResource kotlinx.coroutines  Dispatchers !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  playMjpegStream !kotlinx.coroutines.CoroutineScope  setImageBitmap !kotlinx.coroutines.CoroutineScope  setImageResource !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  Main kotlinx.coroutines.Dispatchers  isCancelled kotlinx.coroutines.Job  AttributeSet okhttp3  BOUNDARY_MARKER okhttp3  
BitmapFactory okhttp3  	ByteArray okhttp3  ByteArrayOutputStream okhttp3  CONTENT_TYPE_MARKER okhttp3  Charsets okhttp3  Context okhttp3  CoroutineScope okhttp3  	ImageView okhttp3  InputStream okhttp3  Job okhttp3  JvmOverloads okhttp3  	ScaleType okhttp3  android okhttp3  copyOfRange okhttp3  indexOf okhttp3  invoke okhttp3  launch okhttp3  playMjpegStream okhttp3  setImageBitmap okhttp3  setImageResource okhttp3  
byteStream okhttp3.ResponseBody  ic_dialog_info android.R.drawable  Config android.graphics.Bitmap  config android.graphics.Bitmap  height android.graphics.Bitmap  width android.graphics.Bitmap  Thread android.view.View  format android.view.View  	javaClass android.view.View  joinToString android.view.View  
plusAssign android.view.View  stackTraceToString android.view.View  take android.view.View  Thread android.widget.ImageView  format android.widget.ImageView  	javaClass android.widget.ImageView  joinToString android.widget.ImageView  
plusAssign android.widget.ImageView  stackTraceToString android.widget.ImageView  take android.widget.ImageView  Thread "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  	javaClass "androidx.compose.foundation.layout  Thread +androidx.compose.foundation.layout.BoxScope  isEmpty +androidx.compose.foundation.layout.BoxScope  	javaClass +androidx.compose.foundation.layout.BoxScope  Thread androidx.compose.material3  isEmpty androidx.compose.material3  	javaClass androidx.compose.material3  Thread androidx.compose.runtime  isEmpty androidx.compose.runtime  	javaClass androidx.compose.runtime  Thread com.example.voicecontrolapp  format com.example.voicecontrolapp  joinToString com.example.voicecontrolapp  
plusAssign com.example.voicecontrolapp  stackTraceToString com.example.voicecontrolapp  	javaClass +com.example.voicecontrolapp.CameraViewModel  	javaClass 5com.example.voicecontrolapp.CameraViewModel.Companion  Thread +com.example.voicecontrolapp.MjpegStreamView  format +com.example.voicecontrolapp.MjpegStreamView  	javaClass +com.example.voicecontrolapp.MjpegStreamView  joinToString +com.example.voicecontrolapp.MjpegStreamView  
plusAssign +com.example.voicecontrolapp.MjpegStreamView  
showTestImage +com.example.voicecontrolapp.MjpegStreamView  stackTraceToString +com.example.voicecontrolapp.MjpegStreamView  take +com.example.voicecontrolapp.MjpegStreamView  Thread 5com.example.voicecontrolapp.MjpegStreamView.Companion  format 5com.example.voicecontrolapp.MjpegStreamView.Companion  	javaClass 5com.example.voicecontrolapp.MjpegStreamView.Companion  joinToString 5com.example.voicecontrolapp.MjpegStreamView.Companion  
plusAssign 5com.example.voicecontrolapp.MjpegStreamView.Companion  stackTraceToString 5com.example.voicecontrolapp.MjpegStreamView.Companion  take 5com.example.voicecontrolapp.MjpegStreamView.Companion  stackTraceToString java.lang.Exception  
currentThread java.lang.Thread  name java.lang.Thread  stackTraceToString kotlin  get kotlin.ByteArray  take kotlin.ByteArray  times 
kotlin.Int  toByte 
kotlin.Int  plus kotlin.Long  
plusAssign kotlin.Long  rem kotlin.Long  format 
kotlin.String  format kotlin.String.Companion  joinToString kotlin.collections  
plusAssign kotlin.collections  joinToString kotlin.collections.List  joinToString kotlin.sequences  format kotlin.text  Thread kotlinx.coroutines  forEach kotlinx.coroutines  format kotlinx.coroutines  	javaClass kotlinx.coroutines  joinToString kotlinx.coroutines  
plusAssign kotlinx.coroutines  stackTraceToString kotlinx.coroutines  take kotlinx.coroutines  stackTraceToString !kotlinx.coroutines.CoroutineScope  Thread okhttp3  format okhttp3  joinToString okhttp3  
plusAssign okhttp3  stackTraceToString okhttp3  headers okhttp3.Request  method okhttp3.Request  message okhttp3.Response  Any com.example.voicecontrolapp  RequestBody com.example.voicecontrolapp  create com.example.voicecontrolapp  
NetworkResult ,com.example.voicecontrolapp.ControlViewModel  	javaClass ,com.example.voicecontrolapp.ControlViewModel  	javaClass 6com.example.voicecontrolapp.ControlViewModel.Companion  Error :com.example.voicecontrolapp.ControlViewModel.NetworkResult  Success :com.example.voicecontrolapp.ControlViewModel.NetworkResult  Any *com.example.voicecontrolapp.NetworkManager  Map *com.example.voicecontrolapp.NetworkManager  RequestBody *com.example.voicecontrolapp.NetworkManager  com *com.example.voicecontrolapp.NetworkManager  create *com.example.voicecontrolapp.NetworkManager  sendControlCommand *com.example.voicecontrolapp.NetworkManager  RequestBody 4com.example.voicecontrolapp.NetworkManager.Companion  com 4com.example.voicecontrolapp.NetworkManager.Companion  create 4com.example.voicecontrolapp.NetworkManager.Companion  Gson com.google.gson  toJson com.google.gson.Gson  
NetworkResult 	java.util  	javaClass 	java.util  Error java.util.NetworkResult  Success java.util.NetworkResult  RequestBody !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  create !kotlinx.coroutines.CoroutineScope  Any okhttp3  Map okhttp3  com okhttp3  create okhttp3  	Companion okhttp3.RequestBody  create okhttp3.RequestBody  create okhttp3.RequestBody.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        