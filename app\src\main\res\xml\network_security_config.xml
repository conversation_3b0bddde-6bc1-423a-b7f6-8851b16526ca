<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 对于开发和测试，完全允许所有HTTP明文传输 -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
    
    <!-- 明确允许本地和私有网络 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">**************</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">********00</domain>
        <!-- 允许整个私有网络段 -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">10.0.0.0</domain>
        <domain includeSubdomains="true">**********</domain>
    </domain-config>
    
    <!-- 调试配置：完全禁用证书验证（仅用于开发） -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>