package com.example.voicecontrolapp

import android.content.Context
import android.media.MediaRecorder
import android.os.Build
import android.util.Log
import java.io.File
import java.io.IOException

class VoiceRecorderManager(private val context: Context) {
    
    private var mediaRecorder: MediaRecorder? = null
    private var isRecording = false
    private var outputFile: String? = null
    
    companion object {
        private const val TAG = "VoiceRecorderManager"
    }
    
    /**
     * 开始录音
     */
    fun startRecording(): Boolean {
        try {
            // 创建录音文件
            val audioDir = File(context.getExternalFilesDir(null), "audio")
            if (!audioDir.exists()) {
                audioDir.mkdirs()
            }
            
            outputFile = File(audioDir, "voice_${System.currentTimeMillis()}.3gp").absolutePath
            
            // 初始化MediaRecorder
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(context)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }
            
            mediaRecorder?.apply {
                // 设置音频源
                setAudioSource(MediaRecorder.AudioSource.MIC)
                // 设置输出格式
                setOutputFormat(MediaRecorder.OutputFormat.THREE_GPP)
                // 设置音频编码器
                setAudioEncoder(MediaRecorder.AudioEncoder.AMR_NB)
                // 设置输出文件
                setOutputFile(outputFile)
                
                try {
                    prepare()
                    start()
                    isRecording = true
                    Log.d(TAG, "录音开始: $outputFile")
                    return true
                } catch (e: IOException) {
                    Log.e(TAG, "录音准备失败", e)
                    release()
                    return false
                }
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "录音开始失败", e)
            return false
        }
        
        return false
    }
    
    /**
     * 停止录音并返回文件路径
     */
    fun stopRecording(): String? {
        try {
            if (isRecording && mediaRecorder != null) {
                mediaRecorder?.apply {
                    try {
                        stop()
                    } catch (e: Exception) {
                        Log.w(TAG, "停止录音时出现异常，可能已经停止: ${e.message}")
                    }
                    release()
                }
                
                isRecording = false
                Log.d(TAG, "录音结束: $outputFile")
                
                // 检查文件是否存在且有内容
                outputFile?.let { filePath ->
                    val file = File(filePath)
                    if (file.exists() && file.length() > 0) {
                        return filePath
                    } else {
                        Log.w(TAG, "录音文件为空或不存在，大小: ${file.length()}")
                        return null
                    }
                }
            } else {
                Log.w(TAG, "停止录音：当前没有在录音或mediaRecorder为null")
                // 即使没有在录音，也要确保状态重置
                isRecording = false
                mediaRecorder?.release()
                mediaRecorder = null
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "停止录音失败", e)
            try {
                mediaRecorder?.release()
            } catch (releaseException: Exception) {
                Log.e(TAG, "释放mediaRecorder失败", releaseException)
            }
            isRecording = false
            mediaRecorder = null
        }
        
        return null
    }
    
    /**
     * 取消录音
     */
    fun cancelRecording() {
        try {
            if (isRecording) {
                mediaRecorder?.apply {
                    stop()
                    release()
                }
                
                // 删除录音文件
                outputFile?.let { filePath ->
                    val file = File(filePath)
                    if (file.exists()) {
                        file.delete()
                        Log.d(TAG, "录音文件已删除: $filePath")
                    }
                }
                
                isRecording = false
                Log.d(TAG, "录音已取消")
            }
        } catch (e: Exception) {
            Log.e(TAG, "取消录音失败", e)
            mediaRecorder?.release()
            isRecording = false
        }
    }
    
    /**
     * 获取当前录音状态
     */
    fun isRecording(): Boolean = isRecording
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            if (isRecording) {
                stopRecording()
            }
            mediaRecorder?.release()
            mediaRecorder = null
        } catch (e: Exception) {
            Log.e(TAG, "释放资源失败", e)
        }
    }
}