package com.example.voicecontrolapp

import android.content.Context
import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.io.File

class MainViewModel : ViewModel() {
    
    private lateinit var voiceRecorderManager: VoiceRecorderManager
    private lateinit var configManager: ConfigManager
    private val networkManager = NetworkManager()
    
    // UI状态
    private val _isRecording = mutableStateOf(false)
    val isRecording: State<Boolean> = _isRecording
    
    private val _isConnected = mutableStateOf(false)
    val isConnected: State<Boolean> = _isConnected
    
    private val _isSending = mutableStateOf(false)
    val isSending: State<Boolean> = _isSending
    
    private val _uploadProgress = mutableStateOf(0)
    val uploadProgress: State<Int> = _uploadProgress
    
    private val _statusMessage = mutableStateOf("准备就绪")
    val statusMessage: State<String> = _statusMessage
    
    private val _raspberryPiUrl = mutableStateOf("192.168.110.21:8080")
    val raspberryPiUrl: State<String> = _raspberryPiUrl
    
    // 权限状态
    private val _hasRecordPermission = mutableStateOf(false)
    val hasRecordPermission: State<Boolean> = _hasRecordPermission
    
    private var connectionCheckJob: Job? = null
    
    companion object {
        private const val TAG = "MainViewModel"
    }
    
    /**
     * 初始化ViewModel
     */
    fun initialize(context: Context) {
        voiceRecorderManager = VoiceRecorderManager(context)
        configManager = ConfigManager.getInstance(context)
        
        // 添加初始化日志
        DebugLogManager.addLog(LogLevel.INFO, TAG, "MainViewModel初始化开始")
        DebugLogManager.addLog(LogLevel.INFO, TAG, "当前配置: ${configManager.getConfigInfo()}")
        
        // 监听配置变化
        configManager.raspberryPiUrl
            .onEach { url ->
                _raspberryPiUrl.value = url
                Log.d(TAG, "树莓派URL已更新: $url")
                DebugLogManager.addLog(LogLevel.INFO, TAG, "配置更新 - 树莓派URL: $url")
            }
            .launchIn(viewModelScope)
        
        startConnectionMonitoring()
        DebugLogManager.addLog(LogLevel.INFO, TAG, "MainViewModel初始化完成，开始连接监控")
    }
    
    /**
     * 设置录音权限状态
     */
    fun setRecordPermission(granted: Boolean) {
        _hasRecordPermission.value = granted
        if (granted) {
            _statusMessage.value = "已获得录音权限"
        } else {
            _statusMessage.value = "需要录音权限才能使用"
        }
    }
    
    /**
     * 开始录音
     */
    fun startRecording() {
        if (!_hasRecordPermission.value) {
            _statusMessage.value = "请先授予录音权限"
            return
        }
        
        if (_isRecording.value) {
            Log.w(TAG, "已经在录音中")
            return
        }
        
        viewModelScope.launch {
            try {
                val success = voiceRecorderManager.startRecording()
                if (success) {
                    _isRecording.value = true
                    _statusMessage.value = "正在录音..."
                    Log.d(TAG, "录音开始成功")
                } else {
                    _statusMessage.value = "录音启动失败"
                    Log.e(TAG, "录音启动失败")
                }
            } catch (e: Exception) {
                _statusMessage.value = "录音启动异常: ${e.message}"
                Log.e(TAG, "录音启动异常", e)
            }
        }
    }
    
    /**
     * 停止录音并发送
     */
    fun stopRecordingAndSend() {
        viewModelScope.launch {
            try {
                // 强制停止录音，无论状态如何
                val audioFilePath = voiceRecorderManager.stopRecording()
                _isRecording.value = false  // 立即更新状态
                
                if (audioFilePath != null) {
                    _statusMessage.value = "录音完成，准备发送..."
                    Log.d(TAG, "录音停止成功，文件: $audioFilePath")
                    
                    // 发送音频文件
                    sendAudioFile(audioFilePath)
                } else {
                    _statusMessage.value = "录音文件保存失败"
                    Log.e(TAG, "录音文件保存失败")
                }
                
            } catch (e: Exception) {
                _isRecording.value = false  // 确保状态重置
                _statusMessage.value = "停止录音异常: ${e.message}"
                Log.e(TAG, "停止录音异常", e)
            }
        }
    }
    
    /**
     * 取消录音
     */
    fun cancelRecording() {
        try {
            voiceRecorderManager.cancelRecording()
            _isRecording.value = false
            _statusMessage.value = "录音已取消"
            Log.d(TAG, "录音已取消")
        } catch (e: Exception) {
            _isRecording.value = false  // 确保状态重置
            _statusMessage.value = "取消录音异常: ${e.message}"
            Log.e(TAG, "取消录音异常", e)
        }
    }
    
    /**
     * 强制停止录音（用于紧急情况）
     */
    fun forceStopRecording() {
        try {
            voiceRecorderManager.release()
            _isRecording.value = false
            _statusMessage.value = "录音已强制停止"
            Log.d(TAG, "录音已强制停止")
        } catch (e: Exception) {
            _isRecording.value = false
            Log.e(TAG, "强制停止录音异常", e)
        }
    }
    
    /**
     * 发送音频文件到树莓派
     */
    private suspend fun sendAudioFile(audioFilePath: String) {
        try {
            _isSending.value = true
            _uploadProgress.value = 0
            
            val audioFile = File(audioFilePath)
            if (!audioFile.exists()) {
                _statusMessage.value = "音频文件不存在"
                return
            }
            
            Log.d(TAG, "开始发送音频文件: $audioFilePath")
            _statusMessage.value = "正在发送到树莓派..."
            
            val currentUrl = if (::configManager.isInitialized) {
                configManager.getCurrentRaspberryPiUrl()
            } else {
                _raspberryPiUrl.value
            }
            
            val result = networkManager.sendVoiceToRaspberryPi(
                audioFile = audioFile,
                raspberryPiUrl = currentUrl,
                onProgress = { progress ->
                    _uploadProgress.value = progress
                }
            )
            
            when (result) {
                is NetworkResult.Success -> {
                    _statusMessage.value = "发送成功！"
                    _uploadProgress.value = 100
                    Log.d(TAG, "音频发送成功: ${result.data}")
                    
                    // 清理临时文件
                    delay(1000)
                    audioFile.delete()
                }
                
                is NetworkResult.Error -> {
                    _statusMessage.value = "发送失败: ${result.message}"
                    _uploadProgress.value = 0
                    Log.e(TAG, "音频发送失败: ${result.message}")
                }
            }
            
        } catch (e: Exception) {
            _statusMessage.value = "发送异常: ${e.message}"
            _uploadProgress.value = 0
            Log.e(TAG, "发送音频文件异常", e)
        } finally {
            _isSending.value = false
            
            // 3秒后重置状态消息
            delay(3000)
            if (!_isRecording.value && !_isSending.value) {
                _statusMessage.value = "准备就绪"
            }
        }
    }
    
    /**
     * 更新树莓派地址
     */
    fun updateRaspberryPiUrl(url: String) {
        if (::configManager.isInitialized) {
            val success = configManager.updateRaspberryPiUrl(url)
            if (success) {
                Log.d(TAG, "树莓派地址更新成功: $url")
                // 重新检查连接
                checkConnection()
            } else {
                _statusMessage.value = "无效的地址格式"
                Log.w(TAG, "树莓派地址更新失败: $url")
            }
        } else {
            Log.e(TAG, "ConfigManager未初始化")
        }
    }
    
    /**
     * 检查连接状态
     */
    private fun checkConnection() {
        viewModelScope.launch {
            try {
                val currentUrl = if (::configManager.isInitialized) {
                    configManager.getCurrentRaspberryPiUrl()
                } else {
                    _raspberryPiUrl.value
                }
                
                val connected = networkManager.checkConnection(currentUrl)
                _isConnected.value = connected
                Log.d(TAG, "连接检查结果: $connected, URL: $currentUrl")
            } catch (e: Exception) {
                _isConnected.value = false
                Log.e(TAG, "连接检查异常", e)
            }
        }
    }
    
    /**
     * 开始连接监控
     */
    private fun startConnectionMonitoring() {
        connectionCheckJob = viewModelScope.launch {
            while (true) {
                checkConnection()
                delay(10000) // 每10秒检查一次连接
            }
        }
    }
    
    /**
     * 手动刷新连接状态
     */
    fun refreshConnection() {
        checkConnection()
    }
    
    /**
     * 执行网络诊断
     */
    fun performNetworkDiagnosis() {
        viewModelScope.launch {
            try {
                val currentUrl = if (::configManager.isInitialized) {
                    configManager.getCurrentRaspberryPiUrl()
                } else {
                    _raspberryPiUrl.value
                }
                
                DebugLogManager.addLog(LogLevel.INFO, TAG, "开始执行网络诊断...")
                val report = networkManager.networkDiagnosis(currentUrl)
                DebugLogManager.addLog(LogLevel.INFO, TAG, "网络诊断完成")
                
            } catch (e: Exception) {
                DebugLogManager.addLog(LogLevel.ERROR, TAG, "网络诊断失败", e)
            }
        }
    }
    
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        
        // 停止录音
        if (_isRecording.value) {
            voiceRecorderManager.cancelRecording()
        }
        
        // 释放录音管理器
        voiceRecorderManager.release()
        
        // 清理网络管理器
        networkManager.cleanup()
        
        // 取消连接监控
        connectionCheckJob?.cancel()
        
        Log.d(TAG, "ViewModel资源已清理")
    }
}