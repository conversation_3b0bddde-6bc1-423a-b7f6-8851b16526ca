package com.example.voicecontrolapp

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.AttributeSet
import android.util.Log
import android.widget.ImageView
import kotlinx.coroutines.*
import okhttp3.*
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * 自定义MJPEG流显示组件
 * 专门用于显示multipart/x-mixed-replace格式的MJPEG流
 */
class MjpegStreamView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ImageView(context, attrs, defStyleAttr) {

    private var streamJob: Job? = null
    private var isStreaming = false
    private var streamUrl: String? = null
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()
    
    companion object {
        private const val TAG = "MjpegStreamView"
        private const val BOUNDARY_MARKER = "--frame"
        private const val CONTENT_TYPE_MARKER = "Content-Type: image/jpeg"
    }
    
    init {
        scaleType = ScaleType.FIT_CENTER
        setBackgroundColor(android.graphics.Color.BLACK)
    }
    
    /**
     * 开始播放MJPEG流
     */
    fun startStream(url: String) {
        Log.d(TAG, "开始MJPEG流: $url")
        stopStream()
        
        streamUrl = url
        isStreaming = true
        
        streamJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                playMjpegStream(url)
            } catch (e: Exception) {
                Log.e(TAG, "MJPEG流播放失败", e)
                withContext(Dispatchers.Main) {
                    // 显示错误图像
                    setImageResource(android.R.drawable.ic_dialog_alert)
                }
            }
        }
    }
    
    /**
     * 停止播放MJPEG流
     */
    fun stopStream() {
        Log.d(TAG, "停止MJPEG流")
        isStreaming = false
        streamJob?.cancel()
        streamJob = null
    }
    
    /**
     * 播放MJPEG流的核心逻辑
     */
    private suspend fun playMjpegStream(url: String) {
        val request = Request.Builder()
            .url(url)
            .addHeader("Accept", "multipart/x-mixed-replace")
            .build()
        
        val response = client.newCall(request).execute()
        
        if (!response.isSuccessful) {
            throw IOException("HTTP错误: ${response.code}")
        }
        
        val inputStream = response.body?.byteStream()
            ?: throw IOException("无法获取响应流")
        
        Log.d(TAG, "开始解析MJPEG流")
        
        try {
            parseMjpegStream(inputStream)
        } finally {
            inputStream.close()
            response.close()
        }
    }
    
    /**
     * 解析MJPEG流并显示帧
     */
    private suspend fun parseMjpegStream(inputStream: InputStream) {
        val buffer = ByteArray(8192)
        val frameBuffer = ByteArrayOutputStream()
        var inFrame = false
        var frameCount = 0
        
        while (isStreaming && !streamJob?.isCancelled!!) {
            val bytesRead = inputStream.read(buffer)
            if (bytesRead == -1) break
            
            val data = String(buffer, 0, bytesRead, Charsets.ISO_8859_1)
            
            // 查找帧边界
            if (data.contains(BOUNDARY_MARKER)) {
                if (inFrame && frameBuffer.size() > 0) {
                    // 处理完整的帧
                    processFrame(frameBuffer.toByteArray())
                    frameCount++
                    frameBuffer.reset()
                }
                inFrame = false
            }
            
            // 查找Content-Type标记
            if (data.contains(CONTENT_TYPE_MARKER)) {
                inFrame = true
                frameBuffer.reset()
                
                // 跳过HTTP头部，找到实际的JPEG数据开始位置
                val jpegStart = data.indexOf("\r\n\r\n")
                if (jpegStart != -1) {
                    val jpegData = buffer.copyOfRange(
                        jpegStart + 4, 
                        bytesRead
                    )
                    frameBuffer.write(jpegData)
                }
            } else if (inFrame) {
                // 收集帧数据
                frameBuffer.write(buffer, 0, bytesRead)
            }
            
            // 每100帧打印一次日志
            if (frameCount % 100 == 0 && frameCount > 0) {
                Log.d(TAG, "已处理 $frameCount 帧")
            }
        }
        
        Log.d(TAG, "MJPEG流解析结束，总共处理了 $frameCount 帧")
    }
    
    /**
     * 处理单个JPEG帧
     */
    private suspend fun processFrame(frameData: ByteArray) {
        try {
            val bitmap = BitmapFactory.decodeByteArray(frameData, 0, frameData.size)
            if (bitmap != null) {
                withContext(Dispatchers.Main) {
                    setImageBitmap(bitmap)
                }
            } else {
                Log.w(TAG, "无法解码JPEG帧，数据大小: ${frameData.size}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理帧失败", e)
        }
    }
    
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopStream()
    }
}
