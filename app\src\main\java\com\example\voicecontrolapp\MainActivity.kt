package com.example.voicecontrolapp

import android.Manifest
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.waitForUpOrCancellation
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.util.Log
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.voicecontrolapp.ui.theme.VoiceControlAppTheme
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale

class MainActivity : ComponentActivity() {
    
    private val mainViewModel: MainViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 添加应用启动日志
        DebugLogManager.addLog(LogLevel.INFO, "MainActivity", "应用启动")
        DebugLogManager.addLog(LogLevel.INFO, "System", DebugLogManager.getSystemInfo())
        
        // 初始化ViewModel
        mainViewModel.initialize(this)
        
        setContent {
            VoiceControlAppTheme {
                VoiceControlApp(mainViewModel)
            }
        }
    }
}

// 导航目标枚举
enum class Screen(val route: String, val title: String, val icon: ImageVector) {
    Voice("voice", "语音控制", Icons.Default.Mic),
    Camera("camera", "摄像头", Icons.Default.Videocam),
    Control("control", "手动控制", Icons.Default.TouchApp),
    Settings("settings", "设置", Icons.Default.Settings),
    DebugLog("debug_log", "调试日志", Icons.Default.BugReport)
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun VoiceControlApp(mainViewModel: MainViewModel) {
    val navController = rememberNavController()
    
    Scaffold(
        bottomBar = {
            BottomNavigationBar(navController = navController)
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Voice.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.Voice.route) {
                VoiceScreen(viewModel = mainViewModel)
            }
            
            composable(Screen.Camera.route) {
                CameraScreen()
            }
            
            composable(Screen.Control.route) {
                ControlScreen()
            }
            
            composable(Screen.Settings.route) {
                SettingsScreen(
                    viewModel = mainViewModel,
                    onDebugLogClick = {
                        navController.navigate(Screen.DebugLog.route)
                    }
                )
            }
            
            composable(Screen.DebugLog.route) {
                DebugLogScreen(
                    onBackClick = {
                        navController.navigateUp()
                    }
                )
            }
        }
    }
}

@Composable
fun BottomNavigationBar(navController: NavController) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    
    NavigationBar {
        Screen.values().forEach { screen ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = screen.icon,
                        contentDescription = screen.title
                    )
                },
                label = { Text(screen.title) },
                selected = currentRoute == screen.route,
                onClick = {
                    navController.navigate(screen.route) {
                        // 避免重复导航到同一个页面
                        popUpTo(navController.graph.startDestinationId)
                        launchSingleTop = true
                    }
                }
            )
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun VoiceScreen(viewModel: MainViewModel) {
    val context = LocalContext.current
    
    // 权限处理
    val recordAudioPermissionState = rememberPermissionState(
        Manifest.permission.RECORD_AUDIO
    ) { granted ->
        viewModel.setRecordPermission(granted)
    }
    
    // 检查权限并更新ViewModel
    LaunchedEffect(recordAudioPermissionState.status.isGranted) {
        viewModel.setRecordPermission(recordAudioPermissionState.status.isGranted)
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        
        // 顶部状态栏
        StatusBar(viewModel = viewModel)
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 权限检查
        if (!recordAudioPermissionState.status.isGranted) {
            PermissionRequestCard(
                onRequestPermission = {
                    recordAudioPermissionState.launchPermissionRequest()
                },
                shouldShowRationale = recordAudioPermissionState.status.shouldShowRationale
            )
        } else {
            // 主要内容区域
            MainContent(viewModel)
        }
    }
}

@Composable
fun StatusBar(
    viewModel: MainViewModel
) {
    val isConnected by viewModel.isConnected
    val raspberryPiUrl by viewModel.raspberryPiUrl
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isConnected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (isConnected) Icons.Default.Wifi else Icons.Default.WifiOff,
                    contentDescription = "连接状态",
                    tint = if (isConnected)
                        MaterialTheme.colorScheme.onPrimaryContainer
                    else
                        MaterialTheme.colorScheme.onErrorContainer
                )
                Spacer(modifier = Modifier.width(8.dp))
                Column {
                    Text(
                        text = if (isConnected) "已连接" else "未连接",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = raspberryPiUrl,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            IconButton(
                onClick = { viewModel.refreshConnection() }
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新连接"
                )
            }
        }
    }
}

@Composable
fun PermissionRequestCard(
    onRequestPermission: () -> Unit,
    shouldShowRationale: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.MicOff,
                contentDescription = "需要权限",
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onErrorContainer
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "需要录音权限",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = if (shouldShowRationale) {
                    "此应用需要录音权限来录制语音并发送到树莓派设备。请授予权限以继续使用。"
                } else {
                    "请授予录音权限以使用语音控制功能。"
                },
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onRequestPermission,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("授予权限")
            }
        }
    }
}

@Composable
fun MainContent(viewModel: MainViewModel) {
    val isRecording by viewModel.isRecording
    val isSending by viewModel.isSending
    val uploadProgress by viewModel.uploadProgress
    val statusMessage by viewModel.statusMessage
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(32.dp)
    ) {
        
        // 状态消息
        StatusMessage(statusMessage, isSending, uploadProgress)
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 语音按钮
        VoiceButton(
            isRecording = isRecording,
            onStartRecording = { viewModel.startRecording() },
            onStopRecording = { viewModel.stopRecordingAndSend() },
            onForceStop = { viewModel.forceStopRecording() },
            enabled = true // 始终启用按钮
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // 使用说明
        UsageInstructions()
    }
}

@Composable
fun StatusMessage(
    message: String,
    isSending: Boolean,
    uploadProgress: Int
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center
            )
            
            if (isSending && uploadProgress > 0) {
                Spacer(modifier = Modifier.height(8.dp))
                LinearProgressIndicator(
                    progress = { uploadProgress / 100f },
                    modifier = Modifier.fillMaxWidth()
                )
                Text(
                    text = "$uploadProgress%",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
fun VoiceButton(
    isRecording: Boolean,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onForceStop: () -> Unit,
    enabled: Boolean
) {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        label = "按钮缩放动画"
    )
    
    // 检测手指松开时停止录音
    LaunchedEffect(isPressed) {
        if (!isPressed && isRecording) {
            Log.d("VoiceButton", "检测到按压状态改变，停止录音")
            onStopRecording()
        }
    }
    
    Box(
        modifier = Modifier
            .size(200.dp)
            .scale(scale)
            .clip(CircleShape)
            .background(
                color = when {
                    !enabled -> MaterialTheme.colorScheme.surfaceVariant
                    isRecording -> MaterialTheme.colorScheme.error
                    else -> MaterialTheme.colorScheme.primary
                }
            )
            .border(
                width = 4.dp,
                color = when {
                    !enabled -> MaterialTheme.colorScheme.outline
                    isRecording -> MaterialTheme.colorScheme.errorContainer
                    else -> MaterialTheme.colorScheme.primaryContainer
                },
                shape = CircleShape
            )
            .pointerInput(enabled) {
                if (enabled) {
                    awaitEachGesture {
                        // 按下开始录音
                        val down = awaitFirstDown()
                        isPressed = true
                        Log.d("VoiceButton", "按钮按下，开始录音")
                        if (!isRecording) {
                            onStartRecording()
                        }
                        
                        // 等待释放
                        val up = waitForUpOrCancellation()
                        isPressed = false
                        Log.d("VoiceButton", "按钮释放，准备停止录音")
                        // 状态变化由LaunchedEffect处理
                    }
                }
            },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = if (isRecording) Icons.Default.Mic else Icons.Default.MicOff,
                contentDescription = if (isRecording) "正在录音" else "开始录音",
                modifier = Modifier.size(48.dp),
                tint = when {
                    !enabled -> MaterialTheme.colorScheme.onSurfaceVariant
                    isRecording -> Color.White
                    else -> Color.White
                }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = if (isRecording) "正在录音" else "按住说话",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = when {
                    !enabled -> MaterialTheme.colorScheme.onSurfaceVariant
                    else -> Color.White
                }
            )
        }
    }
}

@Composable
fun UsageInstructions() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "使用说明",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "• 按住圆形按钮开始录音\n• 松开按钮自动停止并发送\n• 确保树莓派设备已连接\n• 语音将自动传输到树莓派执行",
                style = MaterialTheme.typography.bodyMedium,
                lineHeight = 20.sp
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun VoiceControlAppPreview() {
    VoiceControlAppTheme {
        // Preview内容
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text("语音控制应用预览")
        }
    }
}