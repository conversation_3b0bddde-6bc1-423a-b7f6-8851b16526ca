package com.example.voicecontrolapp

import android.content.Context
import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class ControlViewModel : ViewModel() {
    
    // 网络管理器和配置管理器
    private val networkManager = NetworkManager()
    private lateinit var configManager: ConfigManager
    
    // UI状态
    private val _isConnected = mutableStateOf(false)
    val isConnected: State<Boolean> = _isConnected
    
    private val _connectionStatus = mutableStateOf("检查连接中...")
    val connectionStatus: State<String> = _connectionStatus
    
    private val _robotUrl = mutableStateOf("192.168.1.100:8080")
    val robotUrl: State<String> = _robotUrl
    
    private val _lastCommand = mutableStateOf<String?>(null)
    val lastCommand: State<String?> = _lastCommand
    
    private val _commandHistory = mutableStateOf<List<String>>(emptyList())
    val commandHistory: State<List<String>> = _commandHistory
    
    private var connectionCheckJob: Job? = null
    private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    
    companion object {
        private const val TAG = "ControlViewModel"
        private const val CONTROL_PATH = "/api/control"
    }
    
    /**
     * 初始化ViewModel
     */
    fun initialize(context: Context) {
        configManager = ConfigManager.getInstance(context)
        
        // 监听配置变化
        configManager.raspberryPiUrl
            .onEach { url ->
                _robotUrl.value = url
                Log.d(TAG, "机械狗URL已更新: $url")
            }
            .launchIn(viewModelScope)
        
        startConnectionMonitoring()
    }
    
    /**
     * 发送控制命令
     */
    fun sendCommand(command: String) {
        viewModelScope.launch {
            try {
                val timestamp = dateFormat.format(Date())
                val commandWithTime = "$command (${timestamp})"
                
                Log.d(TAG, "发送控制命令: $command")
                
                // 立即更新UI状态
                _lastCommand.value = commandWithTime
                addToHistory(commandWithTime)
                
                // 发送命令到机械狗
                val success = sendCommandToRobot(command)
                
                if (success) {
                    Log.d(TAG, "命令发送成功: $command")
                } else {
                    Log.e(TAG, "命令发送失败: $command")
                    // 可以在这里添加错误处理，比如重试或显示错误消息
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "发送命令异常: $command", e)
                addToHistory("错误: $command 发送失败")
            }
        }
    }
    
    /**
     * 实际发送命令到机械狗
     */
    private suspend fun sendCommandToRobot(command: String): Boolean {
        return try {
            val currentUrl = if (::configManager.isInitialized) {
                configManager.getCurrentRaspberryPiUrl()
            } else {
                _robotUrl.value
            }
            
            val url = "http://$currentUrl$CONTROL_PATH"
            
            // 构建命令数据
            val commandData = mapOf(
                "action" to command,
                "timestamp" to System.currentTimeMillis(),
                "source" to "manual_control"
            )
            
            // 这里应该使用实际的HTTP请求发送命令
            // 目前使用模拟的方式
            delay(100) // 模拟网络延迟
            
            // 模拟命令执行结果
            when (command) {
                "stop" -> true // 停止命令总是成功
                in listOf("forward", "backward", "left", "right") -> _isConnected.value
                in listOf("stand", "lie", "run", "shake", "spin", "home") -> _isConnected.value
                else -> false
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "发送命令到机械狗失败", e)
            false
        }
    }
    
    /**
     * 更新机械狗地址
     */
    fun updateRobotUrl(url: String) {
        if (::configManager.isInitialized) {
            val success = configManager.updateRaspberryPiUrl(url)
            if (success) {
                Log.d(TAG, "机械狗地址更新成功: $url")
                // 重新检查连接
                checkConnection()
            } else {
                _connectionStatus.value = "无效的地址格式"
                Log.w(TAG, "机械狗地址更新失败: $url")
            }
        } else {
            Log.e(TAG, "ConfigManager未初始化")
        }
    }
    
    /**
     * 添加命令到历史记录
     */
    private fun addToHistory(command: String) {
        val currentHistory = _commandHistory.value.toMutableList()
        currentHistory.add(command)
        
        // 保持最近的20条记录
        if (currentHistory.size > 20) {
            currentHistory.removeAt(0)
        }
        
        _commandHistory.value = currentHistory
    }
    
    /**
     * 检查与机械狗的连接
     */
    private fun checkConnection() {
        viewModelScope.launch {
            try {
                _connectionStatus.value = "检查连接中..."
                
                val currentUrl = if (::configManager.isInitialized) {
                    configManager.getCurrentRaspberryPiUrl()
                } else {
                    _robotUrl.value
                }
                
                // 使用NetworkManager检查连接
                val connected = networkManager.checkConnection(currentUrl)
                
                _isConnected.value = connected
                _connectionStatus.value = if (connected) {
                    "已连接到机械狗"
                } else {
                    "无法连接到机械狗"
                }
                
                Log.d(TAG, "连接检查结果: $connected, URL: $currentUrl")
                
            } catch (e: Exception) {
                _isConnected.value = false
                _connectionStatus.value = "连接检查失败"
                Log.e(TAG, "连接检查异常", e)
            }
        }
    }
    
    /**
     * 开始连接监控
     */
    private fun startConnectionMonitoring() {
        connectionCheckJob = viewModelScope.launch {
            while (true) {
                checkConnection()
                delay(15000) // 每15秒检查一次连接
            }
        }
    }
    
    /**
     * 手动刷新连接
     */
    fun refreshConnection() {
        checkConnection()
    }
    
    /**
     * 紧急停止所有动作
     */
    fun emergencyStop() {
        viewModelScope.launch {
            repeat(3) { // 发送3次停止命令确保执行
                sendCommand("stop")
                delay(100)
            }
            Log.d(TAG, "执行紧急停止")
        }
    }
    
    /**
     * 清空命令历史
     */
    fun clearHistory() {
        _commandHistory.value = emptyList()
        Log.d(TAG, "清空命令历史")
    }
    
    /**
     * 获取连接状态信息
     */
    fun getConnectionInfo(): String {
        val currentUrl = if (::configManager.isInitialized) {
            configManager.getCurrentRaspberryPiUrl()
        } else {
            _robotUrl.value
        }
        
        return buildString {
            appendLine("机械狗地址: $currentUrl")
            appendLine("连接状态: ${if (_isConnected.value) "已连接" else "未连接"}")
            appendLine("最后命令: ${_lastCommand.value ?: "无"}")
            appendLine("命令历史: ${_commandHistory.value.size} 条")
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        connectionCheckJob?.cancel()
        networkManager.cleanup()
        Log.d(TAG, "ControlViewModel已清理")
    }
}